#!/bin/bash
# N100电源控制系统优化版本安装脚本
# 
# 功能：
# 1. 安装串口管理器服务
# 2. 安装关机守护进程服务
# 3. 配置串口权限
# 4. 设置关机通知脚本
# 5. 启动所有服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请使用root权限运行此脚本"
        echo "使用方法: sudo $0"
        exit 1
    fi
}

# 检查依赖
check_dependencies() {
    print_info "检查系统依赖..."
    
    # 检查Python3
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装"
        exit 1
    fi
    
    # 检查pip3
    if ! command -v pip3 &> /dev/null; then
        print_error "pip3 未安装"
        exit 1
    fi
    
    # 检查pyserial
    if ! python3 -c "import serial" &> /dev/null; then
        print_info "安装pyserial..."
        pip3 install pyserial
    fi
    
    print_success "依赖检查完成"
}

# 创建目录
create_directories() {
    print_info "创建必要目录..."
    
    mkdir -p /opt/n100/ctrl
    mkdir -p /var/log
    
    print_success "目录创建完成"
}

# 复制文件
copy_files() {
    print_info "复制程序文件..."
    
    # 获取脚本所在目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    SOURCE_DIR="$(dirname "$SCRIPT_DIR")"
    
    # 复制源代码文件
    cp "$SOURCE_DIR/src/"*.py /opt/n100/ctrl/
    
    # 设置执行权限
    chmod +x /opt/n100/ctrl/*.py
    
    print_success "文件复制完成"
}

# 安装系统服务
install_services() {
    print_info "安装系统服务..."
    
    # 获取脚本所在目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    SOURCE_DIR="$(dirname "$SCRIPT_DIR")"
    
    # 复制服务文件
    cp "$SOURCE_DIR/services/n100-serial-manager.service" /etc/systemd/system/
    cp "$SOURCE_DIR/services/n100-shutdown.service" /etc/systemd/system/
    
    # 重新加载systemd
    systemctl daemon-reload
    
    # 启用服务
    systemctl enable n100-serial-manager.service
    systemctl enable n100-shutdown.service
    
    print_success "系统服务安装完成"
}

# 配置串口权限
configure_serial_permissions() {
    print_info "配置串口权限..."
    
    # 设置串口权限
    if [ -e /dev/ttyS4 ]; then
        chmod 666 /dev/ttyS4
        print_success "串口权限设置完成"
    else
        print_warning "串口设备 /dev/ttyS4 不存在，请检查硬件连接"
    fi
    
    # 添加udev规则，确保重启后权限保持
    cat > /etc/udev/rules.d/99-n100-serial.rules << EOF
# N100串口权限规则
SUBSYSTEM=="tty", KERNEL=="ttyS4", MODE="0666", GROUP="dialout"
EOF
    
    # 重新加载udev规则
    udevadm control --reload-rules
    udevadm trigger
    
    print_success "串口权限配置完成"
}

# 设置关机通知脚本
setup_shutdown_hook() {
    print_info "设置关机通知脚本..."
    
    # 创建关机钩子脚本
    cat > /etc/init.d/n100_shutdown_notify << 'EOF'
#!/bin/bash
# N100关机通知脚本
# 在系统关机时发送关机成功消息给电源板

# 日志函数
log_msg() {
    echo "$(date): $1" >> /var/log/n100_shutdown.log
}

log_msg "关机通知脚本开始执行..."

# 等待文件系统同步
sync
log_msg "文件系统同步完成"

# 发送关机成功消息
python3 /opt/n100/ctrl/shutdown_notify.py

log_msg "关机成功消息已发送"
EOF
    
    # 设置执行权限
    chmod +x /etc/init.d/n100_shutdown_notify
    
    # 添加到关机序列
    update-rc.d n100_shutdown_notify start 01 0 6 .
    
    print_success "关机通知脚本设置完成"
}

# 创建日志文件
create_log_files() {
    print_info "创建日志文件..."
    
    touch /var/log/n100_serial_manager.log
    touch /var/log/n100_shutdown.log
    
    # 设置日志文件权限
    chmod 644 /var/log/n100_serial_manager.log
    chmod 644 /var/log/n100_shutdown.log
    
    print_success "日志文件创建完成"
}

# 启动服务
start_services() {
    print_info "启动服务..."
    
    # 停止旧的服务（如果存在）
    systemctl stop n100-shutdown.service 2>/dev/null || true
    systemctl stop n100-serial-manager.service 2>/dev/null || true
    
    # 启动串口管理器服务
    systemctl start n100-serial-manager.service
    sleep 2
    
    # 启动关机守护进程服务
    systemctl start n100-shutdown.service
    sleep 2
    
    print_success "服务启动完成"
}

# 显示状态
show_status() {
    print_info "系统状态:"
    echo
    
    echo "串口管理器服务状态:"
    systemctl status n100-serial-manager.service --no-pager -l
    echo
    
    echo "关机守护进程服务状态:"
    systemctl status n100-shutdown.service --no-pager -l
    echo
    
    echo "串口设备状态:"
    if [ -e /dev/ttyS4 ]; then
        ls -l /dev/ttyS4
    else
        echo "串口设备 /dev/ttyS4 不存在"
    fi
    echo
}

# 主函数
main() {
    print_info "开始安装N100电源控制系统优化版本..."
    echo
    
    check_root
    check_dependencies
    create_directories
    copy_files
    install_services
    configure_serial_permissions
    setup_shutdown_hook
    create_log_files
    start_services
    
    echo
    print_success "N100电源控制系统优化版本安装完成!"
    echo
    
    show_status
    
    echo
    print_info "使用说明:"
    echo "1. 串口管理器统一管理ttyS4通信，避免冲突"
    echo "2. 关机守护进程自动监听电源板的关机请求"
    echo "3. 收到关机请求后会自动执行系统关机"
    echo "4. 关机时会自动发送关机成功消息给电源板"
    echo "5. 支持多个程序同时使用串口通信"
    echo
    echo "管理命令:"
    echo "  查看串口管理器状态: systemctl status n100-serial-manager"
    echo "  查看关机守护进程状态: systemctl status n100-shutdown"
    echo "  查看日志: tail -f /var/log/n100_*.log"
    echo "  重启服务: systemctl restart n100-serial-manager n100-shutdown"
    echo
}

# 执行主函数
main "$@"
