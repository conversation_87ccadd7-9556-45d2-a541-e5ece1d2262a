#!/bin/bash
# 设置虚拟串口对用于测试

echo "=== 设置虚拟串口对 ==="

# 检查socat是否安装
if ! command -v socat &> /dev/null; then
    echo "安装socat..."
    sudo apt-get update
    sudo apt-get install -y socat
fi

# 创建虚拟串口对
echo "创建虚拟串口对..."
sudo socat -d -d pty,raw,echo=0,link=/tmp/ttyS4_n100 pty,raw,echo=0,link=/tmp/ttyS4_power &
SOCAT_PID=$!

# 等待设备创建
sleep 2

# 设置权限
sudo chmod 666 /tmp/ttyS4_n100
sudo chmod 666 /tmp/ttyS4_power

echo "✅ 虚拟串口对创建成功:"
echo "   N100端: /tmp/ttyS4_n100"
echo "   电源板端: /tmp/ttyS4_power"
echo "   socat PID: $SOCAT_PID"

# 创建符号链接到标准位置
sudo ln -sf /tmp/ttyS4_n100 /dev/ttyS4 2>/dev/null || echo "注意: 无法创建 /dev/ttyS4 符号链接"

echo ""
echo "使用方法:"
echo "1. 在一个终端运行电源板模拟器:"
echo "   python3 src/power_board_simulator.py --port /tmp/ttyS4_power"
echo ""
echo "2. 在另一个终端运行N100程序:"
echo "   python3 src/power_ctrl_cli.py --port /tmp/ttyS4_n100 led normal"
echo ""
echo "3. 停止虚拟串口:"
echo "   kill $SOCAT_PID"

# 保存PID到文件
echo $SOCAT_PID > /tmp/socat_serial.pid
echo "socat PID已保存到 /tmp/socat_serial.pid"
