# N100电源控制系统 - 优化版本

## 📋 项目概述

N100电源控制系统优化版本实现了N100主板与电源板之间的高效串口通信，解决了原版本的串口冲突问题，提供了统一的通信管理架构。

## ✨ 优化特性

### 🔧 核心优化
- **串口管理器**: 统一管理ttyS4串口访问，避免冲突
- **双向通信**: 自动处理通用应答帧，支持N100↔电源板双向通信
- **多客户端支持**: 支持多个程序同时使用串口通信
- **可扩展架构**: 便于后续添加新的通信程序

### 🚀 功能增强
- **自动应答**: 收到消息后自动发送通用应答帧
- **智能重试**: 改进的重试机制，默认10次重试
- **错误恢复**: 完善的错误处理和恢复机制
- **日志记录**: 详细的操作日志和调试信息

### 🔄 关机优化
- **简化流程**: 优化的关机检测和通知机制
- **多重保障**: 多种发送方式确保关机成功消息送达
- **状态检测**: 改进的Linux关机状态检测

## 📁 优化后目录结构

```
n100host/ctrl/
├── src/                              # 核心源代码
│   ├── serial_manager.py            # 串口管理器（新增）
│   ├── protocol.py                  # 通信协议定义（新增）
│   ├── serial_manager_daemon.py     # 串口管理器守护进程（新增）
│   ├── n100_power_ctrl.py          # 电源控制器（优化）
│   ├── power_ctrl_cli.py           # 命令行工具
│   ├── n100_shutdown_daemon.py     # 关机守护进程（优化）
│   └── shutdown_notify.py          # 关机通知脚本（优化）
├── services/                        # 系统服务
│   ├── n100-serial-manager.service # 串口管理器服务（新增）
│   └── n100-shutdown.service       # 关机守护服务（优化）
├── scripts/                         # 安装脚本
│   ├── install_optimized_system.sh # 优化版本安装脚本（新增）
│   └── install_shutdown_system.sh  # 原版安装脚本
├── tests/                           # 测试文件
│   ├── test_optimized_system.py    # 优化版本测试（新增）
│   ├── test_shutdown_system.py     # 关机系统测试
│   └── test_syntax.py              # 语法测试
├── examples/                        # 示例代码
│   ├── optimized_example.py        # 优化版本示例（新增）
│   └── example.py                   # 原版示例
└── docs/                           # 文档
    ├── README_OPTIMIZED.md         # 优化版本说明（本文档）
    ├── README.md                   # 原版说明
    └── USAGE.md                    # 使用指南
```

## 🚀 快速开始

### 1. 安装优化版本
```bash
# 使用优化版本安装脚本
sudo scripts/install_optimized_system.sh
```

### 2. 验证安装
```bash
# 检查服务状态
systemctl status n100-serial-manager
systemctl status n100-shutdown

# 查看日志
tail -f /var/log/n100_serial_manager.log
tail -f /var/log/n100_shutdown.log
```

### 3. 基本使用
```bash
# LED控制（通过串口管理器）
python3 src/power_ctrl_cli.py led normal
python3 src/power_ctrl_cli.py led breath

# 呼吸周期设置
python3 src/power_ctrl_cli.py breath 3

# 运行优化版本示例
python3 examples/optimized_example.py

# 运行测试
python3 tests/test_optimized_system.py
```

## 🔧 架构设计

### 串口管理器架构
```
┌─────────────────────────────────────────────────────────────┐
│                    串口管理器 (SerialManager)                  │
├─────────────────────────────────────────────────────────────┤
│  • 统一管理ttyS4串口访问                                      │
│  • 自动处理通用应答帧                                         │
│  • 支持多客户端并发访问                                       │
│  • 消息队列和回调机制                                         │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────┬─────────────────┬─────────────────┬─────────┐
│   电源控制器     │   关机守护进程   │   自定义程序1    │   ...   │
│ PowerController │ ShutdownDaemon  │  CustomApp1     │         │
└─────────────────┴─────────────────┴─────────────────┴─────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   ttyS4串口     │
                    │   ↕ 电源板      │
                    └─────────────────┘
```

### 通信协议
| 功能 | 命令 | 消息帧 | 说明 |
|------|------|--------|------|
| LED正常模式 | 0x01 | `AA 02 01 00 FF 55` | 设置LED正常模式 |
| LED呼吸模式 | 0x01 | `AA 02 01 01 FE 55` | 设置LED呼吸模式 |
| 1秒呼吸周期 | 0x02 | `AA 02 02 01 FD 55` | 设置1秒呼吸周期 |
| 3秒呼吸周期 | 0x02 | `AA 02 02 03 FB 55` | 设置3秒呼吸周期 |
| 5秒呼吸周期 | 0x02 | `AA 02 02 05 F9 55` | 设置5秒呼吸周期 |
| 关机成功 | 0x03 | `AA 01 03 FD 55` | 关机成功消息 |
| 关机请求 | 0x13 | `AA 01 13 ED 55` | 电源板发送的关机请求 |
| 通用应答 | 0x80 | `AA 01 80 80 55` | ACK应答帧（自动处理） |

## 🛠️ Python API

### 使用串口管理器
```python
from serial_manager import get_serial_manager, MessageFrame
from protocol import CommandType, LEDMode

# 获取串口管理器
manager = get_serial_manager()

# 注册客户端
client_id = "my_app"
manager.register_client(
    client_id=client_id,
    message_callback=on_message_received,
    error_callback=on_error_received
)

# 发送消息
success = manager.send_message(
    client_id=client_id,
    command=CommandType.LED_MODE,
    data=bytes([LEDMode.BREATH]),
    max_retries=3
)

# 注销客户端
manager.unregister_client(client_id)
```

### 使用优化后的电源控制器
```python
from n100_power_ctrl import N100PowerController, LEDMode, BreathPeriod

# 创建控制器（自动使用串口管理器）
controller = N100PowerController(port='/dev/ttyS4', use_manager=True)

# 连接并控制
if controller.connect():
    controller.set_led_breath()         # 设置呼吸模式
    controller.set_breath_3s()          # 3秒周期
    controller.send_shutdown_success()  # 关机成功
    controller.disconnect()
```

## 🔄 服务管理

### 串口管理器服务
```bash
# 启动/停止/重启
systemctl start n100-serial-manager
systemctl stop n100-serial-manager
systemctl restart n100-serial-manager

# 查看状态
systemctl status n100-serial-manager

# 查看日志
journalctl -u n100-serial-manager -f
```

### 关机守护进程服务
```bash
# 启动/停止/重启
systemctl start n100-shutdown
systemctl stop n100-shutdown
systemctl restart n100-shutdown

# 查看状态
systemctl status n100-shutdown

# 查看日志
journalctl -u n100-shutdown -f
```

## 📊 测试验证

```bash
# 运行完整测试套件
python3 tests/test_optimized_system.py

# 运行优化版本示例
python3 examples/optimized_example.py

# 运行性能测试
python3 tests/test_optimized_system.py --performance

# 运行压力测试
python3 tests/test_optimized_system.py --stress
```

## 🔧 配置要求

### 硬件要求
- N100主板
- 电源板（支持串口通信）
- 串口连接（ttyS4）

### 软件要求
- Ubuntu/Linux系统
- Python 3.6+
- pyserial库

### 默认配置参数
- 串口设备: `/dev/ttyS4`
- 波特率: `115200`
- 超时时间: `1.0秒`
- 最大重试次数: `10次`

## 🚨 故障排除

### 常见问题
1. **串口权限不足**: `sudo chmod 666 /dev/ttyS4`
2. **服务启动失败**: 检查依赖服务状态
3. **串口管理器无响应**: 重启串口管理器服务

### 调试命令
```bash
# 检查串口状态
ls -l /dev/ttyS4

# 检查服务依赖
systemctl list-dependencies n100-shutdown

# 查看详细日志
journalctl -u n100-serial-manager -u n100-shutdown --since "1 hour ago"

# 测试串口通信
python3 src/power_ctrl_cli.py test
```

## 📈 性能优势

| 特性 | 原版本 | 优化版本 | 改进 |
|------|--------|----------|------|
| 串口冲突 | 存在 | 已解决 | ✅ 统一管理 |
| 多程序支持 | 不支持 | 支持 | ✅ 多客户端 |
| 自动应答 | 手动 | 自动 | ✅ 简化使用 |
| 错误恢复 | 基础 | 完善 | ✅ 提高可靠性 |
| 扩展性 | 有限 | 良好 | ✅ 易于扩展 |

## 📄 许可证

本项目采用MIT许可证。

---

**注意**: 优化版本与原版本完全兼容，可以无缝升级。建议在生产环境中使用优化版本以获得更好的稳定性和可扩展性。
