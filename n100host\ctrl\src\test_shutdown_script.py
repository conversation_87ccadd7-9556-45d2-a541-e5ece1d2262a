#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
关机脚本测试
测试完整的关机流程
"""

import os
import sys
import time
import threading

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from memory_serial_simulator import MemorySerial, PowerBoardSimulator
    from n100_shutdown_daemon import N100ShutdownDaemon
    from shutdown_notify import main as shutdown_notify_main
except ImportError as e:
    print(f"错误: 无法导入必要模块: {e}")
    sys.exit(1)


class ShutdownScriptTester:
    """关机脚本测试器"""
    
    def __init__(self):
        # 创建内存串口对
        self.n100_serial = MemorySerial("N100")
        self.power_serial = MemorySerial("PowerBoard")
        
        # 连接串口对
        self.n100_serial.connect_peer(self.power_serial)
        
        # 创建电源板模拟器
        self.power_simulator = PowerBoardSimulator(self.power_serial)
        
        # 启动模拟器
        self.power_simulator.start()
        time.sleep(0.5)
    
    def test_shutdown_notify_script(self):
        """测试关机通知脚本"""
        print("=== 测试关机通知脚本 ===")
        
        try:
            # 直接调用关机成功发送函数
            from n100_shutdown_daemon import N100ShutdownDaemon
            
            # 创建临时守护进程实例用于测试
            daemon = N100ShutdownDaemon()
            
            # 替换串口对象
            daemon.port = "memory_serial"
            
            # 模拟关机成功发送
            print("模拟发送关机成功消息...")
            
            # 直接使用内存串口发送
            from protocol import create_shutdown_success_frame
            shutdown_frame = create_shutdown_success_frame()
            shutdown_bytes = shutdown_frame.to_bytes()
            
            self.n100_serial.write(shutdown_bytes)
            print(f"[N100发送] 关机成功: {shutdown_bytes.hex(' ').upper()}")
            
            # 等待处理
            time.sleep(0.5)
            
            # 检查电源板是否收到
            if self.power_simulator.stats['received_commands'] > 0:
                print("✅ 关机通知脚本测试成功")
                return True
            else:
                print("❌ 关机通知脚本测试失败")
                return False
                
        except Exception as e:
            print(f"❌ 关机通知脚本测试异常: {e}")
            return False
    
    def test_shutdown_daemon_simulation(self):
        """测试关机守护进程模拟"""
        print("\n=== 测试关机守护进程模拟 ===")
        
        try:
            # 模拟关机守护进程接收关机请求
            print("1. 电源板发送关机请求...")
            self.power_simulator.send_shutdown_request()
            time.sleep(0.5)
            
            # 检查N100是否收到关机请求
            if self.n100_serial.in_waiting > 0:
                request_data = self.n100_serial.read(self.n100_serial.in_waiting)
                print(f"[N100接收] 关机请求: {request_data.hex(' ').upper()}")
                
                # 2. N100发送ACK应答
                print("2. N100发送ACK应答...")
                from protocol import create_ack_frame
                ack_frame = create_ack_frame()
                ack_bytes = ack_frame.to_bytes()
                self.n100_serial.write(ack_bytes)
                print(f"[N100发送] ACK应答: {ack_bytes.hex(' ').upper()}")
                
                time.sleep(0.5)
                
                # 3. 模拟关机流程
                print("3. 模拟关机流程...")
                print("   - 检测Linux关机状态...")
                print("   - 等待文件系统关闭...")
                print("   - 发送关机成功消息...")
                
                # 4. 发送关机成功消息
                from protocol import create_shutdown_success_frame
                shutdown_frame = create_shutdown_success_frame()
                shutdown_bytes = shutdown_frame.to_bytes()
                self.n100_serial.write(shutdown_bytes)
                print(f"[N100发送] 关机成功: {shutdown_bytes.hex(' ').upper()}")
                
                time.sleep(0.5)
                
                print("✅ 关机守护进程模拟测试成功")
                return True
            else:
                print("❌ N100未收到关机请求")
                return False
                
        except Exception as e:
            print(f"❌ 关机守护进程模拟测试异常: {e}")
            return False
    
    def test_complete_shutdown_flow(self):
        """测试完整关机流程"""
        print("\n=== 测试完整关机流程 ===")
        
        try:
            print("模拟完整关机流程:")
            print("1. 电源板检测到关机条件")
            print("2. 电源板发送关机请求给N100")
            
            # 发送关机请求
            self.power_simulator.send_shutdown_request()
            time.sleep(0.5)
            
            print("3. N100收到关机请求")
            if self.n100_serial.in_waiting > 0:
                request_data = self.n100_serial.read(self.n100_serial.in_waiting)
                print(f"   收到: {request_data.hex(' ').upper()}")
                
                print("4. N100立即发送ACK应答")
                from protocol import create_ack_frame
                ack_frame = create_ack_frame()
                ack_bytes = ack_frame.to_bytes()
                self.n100_serial.write(ack_bytes)
                print(f"   发送: {ack_bytes.hex(' ').upper()}")
                
                time.sleep(0.5)
                
                print("5. N100执行关机脚本")
                print("   - 执行 'sudo shutdown -h now'")
                print("   - 系统开始关机流程")
                
                print("6. 关机脚本在文件系统关闭前执行")
                print("   - 等待文件系统同步")
                print("   - 发送关机成功消息")
                
                # 发送关机成功消息
                from protocol import create_shutdown_success_frame
                shutdown_frame = create_shutdown_success_frame()
                shutdown_bytes = shutdown_frame.to_bytes()
                self.n100_serial.write(shutdown_bytes)
                print(f"   发送: {shutdown_bytes.hex(' ').upper()}")
                
                time.sleep(0.5)
                
                print("7. 电源板收到关机成功消息")
                print("8. 电源板执行断电操作")
                
                print("✅ 完整关机流程测试成功")
                return True
            else:
                print("❌ 完整关机流程测试失败")
                return False
                
        except Exception as e:
            print(f"❌ 完整关机流程测试异常: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        if self.power_simulator:
            self.power_simulator.stop()
    
    def run_all_tests(self):
        """运行所有测试"""
        print("关机脚本功能测试")
        print("=" * 30)
        
        results = {}
        
        try:
            # 1. 测试关机通知脚本
            results['shutdown_notify'] = self.test_shutdown_notify_script()
            
            # 2. 测试关机守护进程模拟
            results['shutdown_daemon'] = self.test_shutdown_daemon_simulation()
            
            # 3. 测试完整关机流程
            results['complete_flow'] = self.test_complete_shutdown_flow()
            
            # 显示结果
            print(f"\n=== 测试结果总结 ===")
            for test_name, result in results.items():
                status = "✅ 通过" if result else "❌ 失败"
                print(f"{test_name}: {status}")
            
            # 显示统计信息
            print(f"\n=== 通信统计 ===")
            print(f"电源板接收命令: {self.power_simulator.stats['received_commands']}")
            print(f"电源板发送ACK: {self.power_simulator.stats['sent_acks']}")
            print(f"电源板发送关机请求: {self.power_simulator.stats['sent_shutdown_requests']}")
            print(f"N100发送字节: {self.n100_serial.bytes_sent}")
            print(f"N100接收字节: {self.n100_serial.bytes_received}")
            
            all_passed = all(results.values())
            if all_passed:
                print("\n🎉 所有关机脚本测试通过！")
            else:
                print("\n❌ 部分关机脚本测试失败")
            
            return all_passed
            
        except Exception as e:
            print(f"\n❌ 测试异常: {e}")
            return False
        finally:
            self.cleanup()


def main():
    """主函数"""
    tester = ShutdownScriptTester()
    
    try:
        success = tester.run_all_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n测试被中断")
        tester.cleanup()
        return 1


if __name__ == "__main__":
    sys.exit(main())
