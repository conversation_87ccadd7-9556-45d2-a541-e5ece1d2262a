#!/bin/bash
# N100关机系统安装脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请以root用户身份运行此脚本"
        exit 1
    fi
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖..."
    
    # 检查Python3
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装"
        exit 1
    fi
    
    # 检查pip3
    if ! command -v pip3 &> /dev/null; then
        print_error "pip3 未安装"
        exit 1
    fi
    
    # 安装pyserial
    print_info "安装pyserial..."
    pip3 install pyserial
    
    print_info "依赖检查完成"
}

# 创建目录结构
create_directories() {
    print_info "创建目录结构..."
    
    mkdir -p /opt/n100/ctrl
    mkdir -p /var/log
    
    print_info "目录创建完成"
}

# 复制文件
copy_files() {
    print_info "复制程序文件..."
    
    # 获取脚本所在目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    
    # 复制Python文件
    cp "$SCRIPT_DIR/../src/n100_power_ctrl.py" /opt/n100/ctrl/
    cp "$SCRIPT_DIR/../src/n100_shutdown_daemon.py" /opt/n100/ctrl/
    cp "$SCRIPT_DIR/../src/shutdown_notify.py" /opt/n100/ctrl/

    # 复制脚本文件
    cp "$SCRIPT_DIR/n100-shutdown-hook.sh" /opt/n100/ctrl/scripts/
    cp "$SCRIPT_DIR/smart_shutdown_start.sh" /opt/n100/ctrl/scripts/
    cp "$SCRIPT_DIR/smart_shutdown_stop.sh" /opt/n100/ctrl/scripts/
    chmod +x /opt/n100/ctrl/scripts/n100-shutdown-hook.sh
    chmod +x /opt/n100/ctrl/scripts/smart_shutdown_start.sh
    chmod +x /opt/n100/ctrl/scripts/smart_shutdown_stop.sh
    
    # 设置权限
    chmod +x /opt/n100/ctrl/n100_shutdown_daemon.py
    chmod +x /opt/n100/ctrl/shutdown_notify.py
    
    print_info "文件复制完成"
}

# 安装systemd服务
install_systemd_service() {
    print_info "安装systemd服务..."
    
    # 复制服务文件
    cp "$SCRIPT_DIR/../services/n100-shutdown.service" /etc/systemd/system/
    
    # 重新加载systemd
    systemctl daemon-reload
    
    # 启用服务
    systemctl enable n100-shutdown.service
    
    print_info "systemd服务安装完成"
}

# 设置关机钩子
setup_shutdown_hook() {
    print_info "设置关机钩子..."
    
    # 创建systemd关机服务
    cat > /etc/systemd/system/n100-shutdown-notify.service << 'EOF'
[Unit]
Description=N100 Shutdown Notification
DefaultDependencies=false
Before=shutdown.target reboot.target halt.target
Requires=shutdown.target

[Service]
Type=oneshot
RemainAfterExit=true
ExecStart=/bin/true
ExecStop=/opt/n100/ctrl/n100-shutdown-hook.sh
TimeoutStopSec=30

[Install]
WantedBy=multi-user.target
EOF
    
    # 重新加载并启用关机通知服务
    systemctl daemon-reload
    systemctl enable n100-shutdown-notify.service
    
    print_info "关机钩子设置完成"
}

# 配置串口权限
configure_serial_permissions() {
    print_info "配置串口权限..."
    
    # 检查串口设备是否存在
    if [ ! -e "/dev/ttyS4" ]; then
        print_warn "串口设备 /dev/ttyS4 不存在，请检查硬件连接"
    else
        # 设置串口权限
        chmod 666 /dev/ttyS4
        
        # 创建udev规则以持久化权限
        cat > /etc/udev/rules.d/99-n100-serial.rules << 'EOF'
# N100串口权限规则
SUBSYSTEM=="tty", KERNEL=="ttyS4", MODE="0666", GROUP="dialout"
EOF
        
        # 重新加载udev规则
        udevadm control --reload-rules
        udevadm trigger
        
        print_info "串口权限配置完成"
    fi
}

# 创建日志文件
create_log_file() {
    print_info "创建日志文件..."
    
    touch /var/log/n100_shutdown.log
    chmod 644 /var/log/n100_shutdown.log
    
    print_info "日志文件创建完成"
}

# 启动服务
start_services() {
    print_info "启动服务..."
    
    # 启动关机守护进程
    systemctl start n100-shutdown.service
    
    # 启动关机通知服务
    systemctl start n100-shutdown-notify.service
    
    print_info "服务启动完成"
}

# 显示状态
show_status() {
    print_info "系统状态:"
    echo
    
    echo "关机守护进程状态:"
    systemctl status n100-shutdown.service --no-pager -l
    echo
    
    echo "关机通知服务状态:"
    systemctl status n100-shutdown-notify.service --no-pager -l
    echo
    
    if [ -e "/dev/ttyS4" ]; then
        echo "串口设备: /dev/ttyS4 存在"
    else
        print_warn "串口设备: /dev/ttyS4 不存在"
    fi
    
    echo "日志文件: /var/log/n100_shutdown.log"
    echo "程序目录: /opt/n100/ctrl"
}

# 主函数
main() {
    print_info "开始安装N100关机系统..."
    
    check_root
    check_dependencies
    create_directories
    copy_files
    install_systemd_service
    setup_shutdown_hook
    configure_serial_permissions
    create_log_file
    start_services
    
    print_info "N100关机系统安装完成!"
    echo
    show_status
    
    echo
    print_info "使用说明:"
    echo "1. 关机守护进程会自动监听电源板的关机请求"
    echo "2. 收到关机请求后会自动执行系统关机"
    echo "3. 在系统关机时会自动发送关机成功消息给电源板"
    echo "4. 查看日志: tail -f /var/log/n100_shutdown.log"
    echo "5. 手动测试: python3 /opt/n100/ctrl/shutdown_notify.py"
}

# 执行主函数
main "$@"
