#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100关机通知脚本
在系统关机时发送关机成功消息给电源板

这个脚本被设计为在系统关机的最后阶段执行，
在文件系统关闭前发送关机成功消息给电源板。
"""

import os
import sys
import time
import logging

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from n100_power_ctrl import N100PowerController
except ImportError:
    # 如果无法导入，将在函数中动态导入serial
    N100PowerController = None


def send_shutdown_success_direct(port='/dev/ttyS4', baudrate=115200):
    """
    直接通过串口发送关机成功消息
    不依赖n100_power_ctrl模块，确保在系统关机时也能工作
    """
    try:
        # 动态导入serial模块
        import serial

        # 关机成功消息帧: AA 01 03 FD 55
        shutdown_frame = bytes([0xAA, 0x01, 0x03, 0xFD, 0x55])

        # 打开串口
        ser = serial.Serial(port, baudrate, timeout=1)
        
        # 发送关机成功消息
        ser.write(shutdown_frame)
        ser.flush()
        
        # 等待一小段时间确保发送完成
        time.sleep(0.1)
        
        # 关闭串口
        ser.close()
        
        return True
        
    except Exception as e:
        # 记录错误但不抛出异常，避免影响关机流程
        try:
            with open('/var/log/n100_shutdown.log', 'a') as f:
                f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} - 发送关机成功消息失败: {e}\n")
        except:
            pass
        return False


def send_shutdown_success_with_controller(port='/dev/ttyS4'):
    """
    使用N100PowerController发送关机成功消息
    """
    try:
        if N100PowerController is None:
            return False

        controller = N100PowerController(port=port, timeout=0.5, max_retries=1)
        
        if controller.connect():
            success = controller.send_shutdown_success()
            controller.disconnect()
            return success
        else:
            return False
            
    except Exception:
        return False


def main():
    """主函数"""
    port = '/dev/ttyS4'
    
    # 记录开始执行
    try:
        with open('/var/log/n100_shutdown.log', 'a') as f:
            f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} - 关机通知脚本开始执行\n")
    except:
        pass
    
    # 等待文件系统同步
    try:
        os.sync()
    except:
        pass
    
    # 尝试使用控制器发送
    success = False
    try:
        success = send_shutdown_success_with_controller(port)
    except:
        pass
    
    # 如果控制器方式失败，使用直接方式
    if not success:
        success = send_shutdown_success_direct(port)
    
    # 记录结果
    try:
        with open('/var/log/n100_shutdown.log', 'a') as f:
            status = "成功" if success else "失败"
            f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} - 关机成功消息发送{status}\n")
    except:
        pass
    
    # 返回状态码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
