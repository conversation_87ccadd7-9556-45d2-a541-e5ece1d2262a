#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100电源控制系统优化版本使用示例

展示功能：
1. 通过串口管理器进行通信
2. 多客户端并发使用
3. 双向通信和自动应答
4. 关机请求处理
5. 错误处理和重试机制
"""

import os
import sys
import time
import threading
from typing import Optional

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'src'))

try:
    from serial_manager import get_serial_manager, MessageFrame, MessageType
    from protocol import CommandType, LEDMode, BreathPeriod
    from n100_power_ctrl import N100PowerController
except ImportError as e:
    print(f"错误: 无法导入必要模块: {e}")
    print("请确保所有模块都在正确的路径下")
    sys.exit(1)


class OptimizedExample:
    """优化版本使用示例类"""
    
    def __init__(self):
        self.manager = None
        self.client_id = f"example_{int(time.time())}"
        self.running = False
        
    def start(self):
        """启动示例"""
        print("N100电源控制系统优化版本使用示例")
        print("=" * 50)
        
        # 获取串口管理器
        self.manager = get_serial_manager()
        
        # 检查串口管理器状态
        if not self.manager.is_running:
            print("串口管理器未运行，尝试启动...")
            if not self.manager.start():
                print("错误: 无法启动串口管理器")
                return False
        
        # 注册客户端
        success = self.manager.register_client(
            client_id=self.client_id,
            message_callback=self._on_message_received,
            error_callback=self._on_error_received
        )
        
        if not success:
            print("错误: 无法注册到串口管理器")
            return False
        
        print(f"已注册客户端: {self.client_id}")
        self.running = True
        
        return True
    
    def stop(self):
        """停止示例"""
        if self.running:
            self.running = False
            if self.manager:
                self.manager.unregister_client(self.client_id)
            print(f"已注销客户端: {self.client_id}")
    
    def _on_message_received(self, message: MessageFrame):
        """消息接收回调"""
        print(f"[接收] 命令=0x{message.command:02X}, 数据={message.data.hex(' ').upper()}")
        
        # 处理关机请求
        if message.command == CommandType.SHUTDOWN_REQUEST:
            print("[警告] 收到关机请求!")
    
    def _on_error_received(self, error_msg: str):
        """错误接收回调"""
        print(f"[错误] {error_msg}")
    
    def send_led_command(self, mode: LEDMode):
        """发送LED命令"""
        print(f"\n发送LED命令: {'呼吸模式' if mode == LEDMode.BREATH else '正常模式'}")
        
        success = self.manager.send_message(
            client_id=self.client_id,
            command=CommandType.LED_MODE,
            data=bytes([mode]),
            max_retries=3
        )
        
        if success:
            print("LED命令发送成功")
        else:
            print("LED命令发送失败")
        
        return success
    
    def send_breath_command(self, period: BreathPeriod):
        """发送呼吸周期命令"""
        print(f"\n发送呼吸周期命令: {period}秒")
        
        success = self.manager.send_message(
            client_id=self.client_id,
            command=CommandType.BREATH_PERIOD,
            data=bytes([period]),
            max_retries=3
        )
        
        if success:
            print("呼吸周期命令发送成功")
        else:
            print("呼吸周期命令发送失败")
        
        return success
    
    def send_shutdown_success(self):
        """发送关机成功命令"""
        print("\n发送关机成功命令")
        
        success = self.manager.send_message(
            client_id=self.client_id,
            command=CommandType.SHUTDOWN_SUCCESS,
            data=b'',
            max_retries=3
        )
        
        if success:
            print("关机成功命令发送成功")
        else:
            print("关机成功命令发送失败")
        
        return success
    
    def run_basic_test(self):
        """运行基本功能测试"""
        print("\n=== 基本功能测试 ===")
        
        # 测试LED控制
        self.send_led_command(LEDMode.NORMAL)
        time.sleep(1)
        
        self.send_led_command(LEDMode.BREATH)
        time.sleep(1)
        
        # 测试呼吸周期控制
        for period in [BreathPeriod.PERIOD_1S, BreathPeriod.PERIOD_3S, BreathPeriod.PERIOD_5S]:
            self.send_breath_command(period)
            time.sleep(1)
        
        # 测试关机成功消息
        self.send_shutdown_success()
        time.sleep(1)
    
    def run_concurrent_test(self):
        """运行并发测试"""
        print("\n=== 并发测试 ===")
        
        def worker(worker_id):
            """工作线程"""
            client_id = f"worker_{worker_id}_{int(time.time())}"
            
            # 注册工作线程客户端
            success = self.manager.register_client(client_id)
            if not success:
                print(f"工作线程 {worker_id} 注册失败")
                return
            
            try:
                # 发送多个命令
                for i in range(3):
                    mode = LEDMode.BREATH if i % 2 else LEDMode.NORMAL
                    success = self.manager.send_message(
                        client_id=client_id,
                        command=CommandType.LED_MODE,
                        data=bytes([mode]),
                        max_retries=1
                    )
                    print(f"工作线程 {worker_id} 命令 {i+1}: {'成功' if success else '失败'}")
                    time.sleep(0.5)
            
            finally:
                # 注销客户端
                self.manager.unregister_client(client_id)
                print(f"工作线程 {worker_id} 已完成")
        
        # 启动多个工作线程
        threads = []
        for i in range(3):
            thread = threading.Thread(target=worker, args=(i+1,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        print("并发测试完成")
    
    def run_compatibility_test(self):
        """运行兼容性测试"""
        print("\n=== 兼容性测试 ===")
        
        # 测试传统电源控制器
        print("测试传统电源控制器兼容性...")
        
        controller = N100PowerController(port='/dev/ttyS4', use_manager=True)
        
        if controller.connect():
            print("传统控制器连接成功")
            
            # 测试各种命令
            controller.set_led_mode(LEDMode.BREATH)
            time.sleep(0.5)
            
            controller.set_breath_period(BreathPeriod.PERIOD_3S)
            time.sleep(0.5)
            
            controller.send_shutdown_success()
            time.sleep(0.5)
            
            controller.disconnect()
            print("传统控制器测试完成")
        else:
            print("传统控制器连接失败")


def main():
    """主函数"""
    example = OptimizedExample()
    
    try:
        # 启动示例
        if not example.start():
            return 1
        
        # 运行各种测试
        example.run_basic_test()
        example.run_concurrent_test()
        example.run_compatibility_test()
        
        print("\n=== 交互模式 ===")
        print("输入命令进行交互测试:")
        print("  1 - LED正常模式")
        print("  2 - LED呼吸模式")
        print("  3 - 1秒呼吸周期")
        print("  4 - 3秒呼吸周期")
        print("  5 - 5秒呼吸周期")
        print("  6 - 关机成功消息")
        print("  q - 退出")
        
        while example.running:
            try:
                cmd = input("\n请输入命令: ").strip().lower()
                
                if cmd == 'q':
                    break
                elif cmd == '1':
                    example.send_led_command(LEDMode.NORMAL)
                elif cmd == '2':
                    example.send_led_command(LEDMode.BREATH)
                elif cmd == '3':
                    example.send_breath_command(BreathPeriod.PERIOD_1S)
                elif cmd == '4':
                    example.send_breath_command(BreathPeriod.PERIOD_3S)
                elif cmd == '5':
                    example.send_breath_command(BreathPeriod.PERIOD_5S)
                elif cmd == '6':
                    example.send_shutdown_success()
                else:
                    print("无效命令")
                    
            except KeyboardInterrupt:
                break
        
        return 0
        
    except Exception as e:
        print(f"示例运行异常: {e}")
        return 1
    
    finally:
        example.stop()


if __name__ == "__main__":
    sys.exit(main())
