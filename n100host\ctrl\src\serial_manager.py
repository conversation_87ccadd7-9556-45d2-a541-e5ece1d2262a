#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
串口通信管理器
统一管理N100与电源板之间的ttyS4串口通信，支持多客户端访问和双向通信

功能特性：
1. 统一串口访问管理，避免冲突
2. 自动处理通用应答帧
3. 支持多客户端同时使用
4. 消息队列和回调机制
5. 双向通信支持
"""

import serial
import time
import threading
import queue
import logging
from typing import Optional, Callable, Dict, Any
from enum import IntEnum
from dataclasses import dataclass


class MessageType(IntEnum):
    """消息类型枚举"""
    LED_MODE = 0x01        # LED模式设置命令
    BREATH_PERIOD = 0x02   # 呼吸灯周期设置命令
    SHUTDOWN = 0x03        # 关机成功消息
    SHUTDOWN_REQ = 0x13    # 关机请求命令（电源板发送给N100）
    ACK = 0x80            # 通用应答命令


@dataclass
class MessageFrame:
    """消息帧数据结构"""
    command: int
    data: bytes = b''
    source: str = 'unknown'
    timestamp: float = 0.0
    
    def __post_init__(self):
        if self.timestamp == 0.0:
            self.timestamp = time.time()


class SerialManager:
    """串口通信管理器"""
    
    # 协议常量
    FRAME_HEADER = 0xAA
    FRAME_TAIL = 0x55
    
    def __init__(self, port: str = '/dev/ttyS4', baudrate: int = 115200, 
                 timeout: float = 1.0):
        """
        初始化串口管理器
        
        参数:
            port (str): 串口设备路径
            baudrate (int): 波特率
            timeout (float): 读取超时时间
        """
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.serial = None
        self.is_running = False
        
        # 线程和锁
        self._read_thread = None
        self._write_lock = threading.Lock()
        self._clients_lock = threading.Lock()
        
        # 接收缓冲区
        self._rx_buffer = bytearray()
        self._rx_lock = threading.Lock()
        
        # 客户端管理
        self._clients: Dict[str, Dict[str, Any]] = {}
        self._message_queue = queue.Queue()

        # ACK等待机制
        self._ack_event = threading.Event()
        self._waiting_for_ack = False

        # 日志
        self.logger = logging.getLogger('SerialManager')
        
    def start(self) -> bool:
        """启动串口管理器"""
        try:
            # 打开串口
            self.serial = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=self.timeout
            )
            
            self.is_running = True
            
            # 启动读取线程
            self._read_thread = threading.Thread(target=self._read_loop, daemon=True)
            self._read_thread.start()
            
            self.logger.info(f"串口管理器已启动: {self.port}")
            return True
            
        except Exception as e:
            self.logger.error(f"启动串口管理器失败: {e}")
            return False
    
    def stop(self):
        """停止串口管理器"""
        self.is_running = False
        
        if self._read_thread and self._read_thread.is_alive():
            self._read_thread.join(timeout=2.0)
        
        if self.serial and self.serial.is_open:
            self.serial.close()
        
        self.logger.info("串口管理器已停止")
    
    def register_client(self, client_id: str, 
                       message_callback: Optional[Callable[[MessageFrame], None]] = None,
                       error_callback: Optional[Callable[[str], None]] = None) -> bool:
        """
        注册客户端
        
        参数:
            client_id (str): 客户端ID
            message_callback: 消息接收回调函数
            error_callback: 错误回调函数
            
        返回:
            bool: 注册是否成功
        """
        with self._clients_lock:
            if client_id in self._clients:
                self.logger.warning(f"客户端 {client_id} 已存在")
                return False
            
            self._clients[client_id] = {
                'message_callback': message_callback,
                'error_callback': error_callback,
                'registered_time': time.time()
            }
            
            self.logger.info(f"客户端 {client_id} 已注册")
            return True
    
    def unregister_client(self, client_id: str):
        """注销客户端"""
        with self._clients_lock:
            if client_id in self._clients:
                del self._clients[client_id]
                self.logger.info(f"客户端 {client_id} 已注销")
    
    def send_message(self, client_id: str, command: int, data: bytes = b'', 
                    wait_ack: bool = True, max_retries: int = 3) -> bool:
        """
        发送消息
        
        参数:
            client_id (str): 发送方客户端ID
            command (int): 命令字节
            data (bytes): 数据字节
            wait_ack (bool): 是否等待ACK应答
            max_retries (int): 最大重试次数
            
        返回:
            bool: 发送是否成功
        """
        if not self.is_running or not self.serial:
            self.logger.error("串口管理器未运行")
            return False
        
        frame = self._create_frame(command, data)
        
        for attempt in range(max_retries):
            try:
                with self._write_lock:
                    # 清空输入缓冲区
                    self.serial.reset_input_buffer()
                    
                    # 发送帧
                    self.logger.debug(f"[{client_id}] 发送消息 (尝试 {attempt + 1}/{max_retries}): {frame.hex(' ').upper()}")
                    self.serial.write(frame)
                    self.serial.flush()
                
                # 如果需要等待ACK
                if wait_ack:
                    if self._wait_for_ack(timeout=self.timeout):
                        self.logger.info(f"[{client_id}] 消息发送成功")
                        return True
                    else:
                        self.logger.warning(f"[{client_id}] 未收到ACK应答，准备重试...")
                else:
                    # 不等待ACK，直接返回成功
                    self.logger.info(f"[{client_id}] 消息已发送（不等待ACK）")
                    return True
                    
            except Exception as e:
                self.logger.error(f"[{client_id}] 发送消息异常: {e}")
                self._notify_error(client_id, f"发送消息异常: {e}")
        
        self.logger.error(f"[{client_id}] 消息发送失败，已重试 {max_retries} 次")
        return False
    
    def _create_frame(self, command: int, data: bytes = b'') -> bytes:
        """创建消息帧"""
        length = len(data) + 1  # 数据长度 + 命令长度
        
        # 校验和只对命令+数据计算
        checksum_data = bytes([command]) + data
        checksum = self._calculate_checksum(checksum_data)
        
        frame = bytes([self.FRAME_HEADER, length, command]) + data + bytes([checksum, self.FRAME_TAIL])
        return frame
    
    def _calculate_checksum(self, data: bytes) -> int:
        """计算校验和"""
        checksum = sum(data) & 0xFF
        return ((~checksum) + 1) & 0xFF
    
    def _read_loop(self):
        """读取循环线程"""
        while self.is_running:
            try:
                if self.serial and self.serial.in_waiting > 0:
                    data = self.serial.read(self.serial.in_waiting)
                    self.logger.debug(f"接收原始数据: {data.hex(' ').upper()}")
                    
                    with self._rx_lock:
                        self._rx_buffer.extend(data)
                        self._parse_messages()
                
                time.sleep(0.01)  # 短暂休眠避免CPU占用过高
                
            except Exception as e:
                self.logger.error(f"读取循环异常: {e}")
                self._notify_all_error(f"读取循环异常: {e}")
                time.sleep(0.1)
    
    def _parse_messages(self):
        """解析接收到的消息"""
        while len(self._rx_buffer) >= 5:  # 最小帧长度
            # 查找帧头
            header_pos = -1
            for i in range(len(self._rx_buffer)):
                if self._rx_buffer[i] == self.FRAME_HEADER:
                    header_pos = i
                    break
            
            if header_pos == -1:
                # 没有找到帧头，清空缓冲区
                self._rx_buffer.clear()
                break
            
            if header_pos > 0:
                # 丢弃帧头前的数据
                self._rx_buffer = self._rx_buffer[header_pos:]
            
            # 检查是否有完整的帧
            if len(self._rx_buffer) < 5:
                break
            
            length = self._rx_buffer[1]
            frame_length = 4 + length  # 帧头 + 长度 + 数据 + 校验 + 帧尾
            
            if len(self._rx_buffer) < frame_length:
                break
            
            # 提取完整帧
            frame_data = bytes(self._rx_buffer[:frame_length])
            self._rx_buffer = self._rx_buffer[frame_length:]
            
            # 验证帧
            if self._validate_frame(frame_data):
                command = frame_data[2]
                data = frame_data[3:3+length-1] if length > 1 else b''
                
                # 创建消息帧
                message = MessageFrame(command=command, data=data, source='power_board')
                
                # 处理消息
                self._handle_message(message)
            else:
                self.logger.warning(f"无效帧: {frame_data.hex(' ').upper()}")
    
    def _validate_frame(self, frame: bytes) -> bool:
        """验证帧的完整性"""
        if len(frame) < 5:
            return False
        
        if frame[0] != self.FRAME_HEADER or frame[-1] != self.FRAME_TAIL:
            return False
        
        length = frame[1]
        if len(frame) != 4 + length:
            return False
        
        # 验证校验和
        command = frame[2]
        data = frame[3:3+length-1] if length > 1 else b''
        expected_checksum = self._calculate_checksum(bytes([command]) + data)
        actual_checksum = frame[3+length-1]
        
        return expected_checksum == actual_checksum
    
    def _handle_message(self, message: MessageFrame):
        """处理接收到的消息"""
        self.logger.info(f"收到消息: 命令=0x{message.command:02X}, 数据={message.data.hex(' ').upper()}")

        # 处理ACK消息
        if message.command == MessageType.ACK:
            self.logger.info("收到通用应答")
            if self._waiting_for_ack:
                self._ack_event.set()  # 通知等待ACK的线程
            # 通知客户端收到ACK
            self._notify_all_ack(message)
            return

        # 如果是关机请求或其他需要ACK的消息，自动发送ACK
        if message.command in [MessageType.SHUTDOWN_REQ]:
            self._send_ack()

        # 通知所有客户端
        self._notify_all_message(message)
    
    def _send_ack(self):
        """发送通用应答帧"""
        try:
            # 正确的ACK帧格式: AA 01 80 80 55
            ack_frame = bytes([self.FRAME_HEADER, 0x01, MessageType.ACK, 0x80, self.FRAME_TAIL])
            with self._write_lock:
                self.serial.write(ack_frame)
                self.serial.flush()
            self.logger.info("已发送ACK应答")
        except Exception as e:
            self.logger.error(f"发送ACK失败: {e}")

    def _wait_for_ack(self, timeout: float = 1.0) -> bool:
        """等待ACK应答"""
        # 设置等待ACK标志
        self._waiting_for_ack = True
        self._ack_event.clear()

        try:
            # 等待ACK事件或超时
            received = self._ack_event.wait(timeout)
            if received:
                self.logger.debug("收到ACK应答")
                return True
            else:
                self.logger.debug(f"等待ACK超时 ({timeout}秒)")
                return False
        finally:
            # 清除等待标志
            self._waiting_for_ack = False
    
    def _notify_all_message(self, message: MessageFrame):
        """通知所有客户端收到消息"""
        with self._clients_lock:
            for client_id, client_info in self._clients.items():
                callback = client_info.get('message_callback')
                if callback:
                    try:
                        callback(message)
                    except Exception as e:
                        self.logger.error(f"客户端 {client_id} 消息回调异常: {e}")
    
    def _notify_all_error(self, error_msg: str):
        """通知所有客户端发生错误"""
        with self._clients_lock:
            for client_id, client_info in self._clients.items():
                self._notify_error(client_id, error_msg)
    
    def _notify_error(self, client_id: str, error_msg: str):
        """通知指定客户端发生错误"""
        with self._clients_lock:
            if client_id in self._clients:
                callback = self._clients[client_id].get('error_callback')
                if callback:
                    try:
                        callback(error_msg)
                    except Exception as e:
                        self.logger.error(f"客户端 {client_id} 错误回调异常: {e}")

    def _notify_all_ack(self, message: MessageFrame):
        """通知所有客户端收到ACK"""
        with self._clients_lock:
            for client_id, client_info in self._clients.items():
                callback = client_info.get('message_callback')
                if callback:
                    try:
                        # 创建ACK消息
                        ack_message = MessageFrame(
                            command=MessageType.ACK,
                            data=b'',
                            source='power_board'
                        )
                        callback(ack_message)
                        self.logger.debug(f"已通知客户端 {client_id} 收到ACK")
                    except Exception as e:
                        self.logger.error(f"客户端 {client_id} ACK回调异常: {e}")


# 全局串口管理器实例
_serial_manager_instance = None
_manager_lock = threading.Lock()


def get_serial_manager() -> SerialManager:
    """获取全局串口管理器实例（单例模式）"""
    global _serial_manager_instance
    
    with _manager_lock:
        if _serial_manager_instance is None:
            _serial_manager_instance = SerialManager()
        return _serial_manager_instance


def start_serial_manager() -> bool:
    """启动全局串口管理器"""
    manager = get_serial_manager()
    return manager.start()


def stop_serial_manager():
    """停止全局串口管理器"""
    global _serial_manager_instance
    
    with _manager_lock:
        if _serial_manager_instance:
            _serial_manager_instance.stop()
            _serial_manager_instance = None
