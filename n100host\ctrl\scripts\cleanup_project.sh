#!/bin/bash

# N100电源控制系统项目清理脚本
# 清理临时文件、缓存和不必要的文件

echo "=== N100项目清理脚本 ==="

# 获取项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
echo "项目根目录: $PROJECT_ROOT"

cd "$PROJECT_ROOT"

# 清理Python缓存
echo "清理Python缓存..."
find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
find . -name "*.pyc" -delete 2>/dev/null || true
find . -name "*.pyo" -delete 2>/dev/null || true

# 清理临时文件
echo "清理临时文件..."
find . -name "*.tmp" -delete 2>/dev/null || true
find . -name "*.log" -delete 2>/dev/null || true
find . -name "*~" -delete 2>/dev/null || true
find . -name ".DS_Store" -delete 2>/dev/null || true

# 清理测试输出
echo "清理测试输出..."
rm -rf .pytest_cache/ 2>/dev/null || true
rm -rf htmlcov/ 2>/dev/null || true
rm -rf .coverage 2>/dev/null || true

# 清理构建文件
echo "清理构建文件..."
rm -rf build/ 2>/dev/null || true
rm -rf dist/ 2>/dev/null || true
rm -rf *.egg-info/ 2>/dev/null || true

# 显示清理后的项目结构
echo ""
echo "=== 清理后的项目结构 ==="
tree -I '__pycache__|*.pyc|*.pyo|*.tmp|*.log' . 2>/dev/null || find . -type f -name "*.py" | head -20

echo ""
echo "✅ 项目清理完成！"
