#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试消息帧格式
验证发送的消息帧是否正确
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from n100_power_ctrl import N100PowerController, LEDMode, BreathPeriod, PowerCommand
    from protocol import (
        create_led_mode_frame, create_breath_period_frame, 
        create_shutdown_success_frame, create_ack_frame,
        validate_frame, calculate_checksum
    )
except ImportError as e:
    print(f"错误: 无法导入必要模块: {e}")
    sys.exit(1)


def test_frame_creation():
    """测试帧创建"""
    print("=== 测试消息帧创建 ===")
    
    # 测试LED帧
    print("\n1. LED模式帧:")
    led_normal = create_led_mode_frame(LEDMode.NORMAL)
    led_breath = create_led_mode_frame(LEDMode.BREATH)
    
    print(f"   LED正常模式: {led_normal.to_bytes().hex(' ').upper()}")
    print(f"   LED呼吸模式: {led_breath.to_bytes().hex(' ').upper()}")
    
    # 测试呼吸周期帧
    print("\n2. 呼吸周期帧:")
    breath_1s = create_breath_period_frame(BreathPeriod.PERIOD_1S)
    breath_3s = create_breath_period_frame(BreathPeriod.PERIOD_3S)
    breath_5s = create_breath_period_frame(BreathPeriod.PERIOD_5S)
    
    print(f"   1秒周期: {breath_1s.to_bytes().hex(' ').upper()}")
    print(f"   3秒周期: {breath_3s.to_bytes().hex(' ').upper()}")
    print(f"   5秒周期: {breath_5s.to_bytes().hex(' ').upper()}")
    
    # 测试关机成功帧
    print("\n3. 关机成功帧:")
    shutdown = create_shutdown_success_frame()
    print(f"   关机成功: {shutdown.to_bytes().hex(' ').upper()}")
    
    # 测试ACK帧
    print("\n4. ACK应答帧:")
    ack = create_ack_frame()
    print(f"   ACK应答: {ack.to_bytes().hex(' ').upper()}")
    
    return True


def test_frame_validation():
    """测试帧验证"""
    print("\n=== 测试消息帧验证 ===")
    
    # 测试有效帧
    valid_frames = [
        bytes([0xAA, 0x02, 0x01, 0x00, 0xFF, 0x55]),  # LED正常
        bytes([0xAA, 0x02, 0x01, 0x01, 0xFE, 0x55]),  # LED呼吸
        bytes([0xAA, 0x02, 0x02, 0x01, 0xFD, 0x55]),  # 1秒周期
        bytes([0xAA, 0x02, 0x02, 0x03, 0xFB, 0x55]),  # 3秒周期
        bytes([0xAA, 0x02, 0x02, 0x05, 0xF9, 0x55]),  # 5秒周期
        bytes([0xAA, 0x01, 0x03, 0xFD, 0x55]),        # 关机成功
        bytes([0xAA, 0x01, 0x80, 0x80, 0x55]),        # ACK应答
    ]
    
    frame_names = [
        "LED正常模式", "LED呼吸模式", "1秒呼吸周期", "3秒呼吸周期", 
        "5秒呼吸周期", "关机成功", "ACK应答"
    ]
    
    print("\n有效帧验证:")
    for i, (frame, name) in enumerate(zip(valid_frames, frame_names)):
        is_valid = validate_frame(frame)
        status = "✅" if is_valid else "❌"
        print(f"   {status} {name}: {frame.hex(' ').upper()}")
    
    # 测试无效帧
    print("\n无效帧验证:")
    invalid_frames = [
        bytes([0xBB, 0x02, 0x01, 0x00, 0xFF, 0x55]),  # 错误帧头
        bytes([0xAA, 0x02, 0x01, 0x00, 0xFF, 0x44]),  # 错误帧尾
        bytes([0xAA, 0x02, 0x01, 0x00, 0xFE, 0x55]),  # 错误校验和
        bytes([0xAA, 0x02, 0x01]),                     # 不完整帧
    ]
    
    invalid_names = ["错误帧头", "错误帧尾", "错误校验和", "不完整帧"]
    
    for frame, name in zip(invalid_frames, invalid_names):
        is_valid = validate_frame(frame)
        status = "❌" if not is_valid else "⚠️"
        print(f"   {status} {name}: {frame.hex(' ').upper()}")
    
    return True


def test_checksum_calculation():
    """测试校验和计算"""
    print("\n=== 测试校验和计算 ===")
    
    test_cases = [
        (bytes([0x01, 0x00]), "LED正常模式"),
        (bytes([0x01, 0x01]), "LED呼吸模式"),
        (bytes([0x02, 0x01]), "1秒呼吸周期"),
        (bytes([0x02, 0x03]), "3秒呼吸周期"),
        (bytes([0x02, 0x05]), "5秒呼吸周期"),
        (bytes([0x03]), "关机成功"),
        (bytes([0x80]), "ACK应答"),
    ]
    
    for data, name in test_cases:
        checksum = calculate_checksum(data)
        print(f"   {name}: 数据={data.hex(' ').upper()}, 校验和=0x{checksum:02X}")
    
    return True


def test_controller_frame_creation():
    """测试控制器帧创建"""
    print("\n=== 测试控制器帧创建 ===")
    
    try:
        # 创建控制器实例（不连接）
        controller = N100PowerController(use_manager=False)
        
        # 测试帧创建方法
        print("\n控制器内部帧创建:")
        
        # LED正常模式帧
        led_normal_frame = controller._create_frame(PowerCommand.LED_MODE, bytes([LEDMode.NORMAL]))
        print(f"   LED正常模式: {led_normal_frame.hex(' ').upper()}")
        
        # LED呼吸模式帧
        led_breath_frame = controller._create_frame(PowerCommand.LED_MODE, bytes([LEDMode.BREATH]))
        print(f"   LED呼吸模式: {led_breath_frame.hex(' ').upper()}")
        
        # 呼吸周期帧
        breath_3s_frame = controller._create_frame(PowerCommand.BREATH_PERIOD, bytes([BreathPeriod.PERIOD_3S]))
        print(f"   3秒呼吸周期: {breath_3s_frame.hex(' ').upper()}")
        
        # 关机成功帧
        shutdown_frame = controller._create_frame(PowerCommand.SHUTDOWN)
        print(f"   关机成功: {shutdown_frame.hex(' ').upper()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 控制器帧创建测试失败: {e}")
        return False


def main():
    """主函数"""
    print("N100消息帧格式测试")
    print("=" * 40)
    
    results = {}
    
    # 1. 测试帧创建
    results['frame_creation'] = test_frame_creation()
    
    # 2. 测试帧验证
    results['frame_validation'] = test_frame_validation()
    
    # 3. 测试校验和计算
    results['checksum'] = test_checksum_calculation()
    
    # 4. 测试控制器帧创建
    results['controller_frames'] = test_controller_frame_creation()
    
    # 显示结果
    print(f"\n=== 测试结果总结 ===")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    all_passed = all(results.values())
    if all_passed:
        print("\n🎉 所有消息帧格式测试通过！")
        print("\n标准消息帧格式:")
        print("   LED正常模式: AA 02 01 00 FF 55")
        print("   LED呼吸模式: AA 02 01 01 FE 55")
        print("   1秒呼吸周期: AA 02 02 01 FD 55")
        print("   3秒呼吸周期: AA 02 02 03 FB 55")
        print("   5秒呼吸周期: AA 02 02 05 F9 55")
        print("   关机成功:   AA 01 03 FD 55")
        print("   ACK应答:    AA 01 80 80 55")
    else:
        print("\n❌ 部分测试失败，需要检查消息帧格式")
    
    return 0 if all_passed else 1


if __name__ == "__main__":
    sys.exit(main())
