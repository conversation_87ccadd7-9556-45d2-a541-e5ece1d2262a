#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100与电源板通信协议定义
统一定义消息帧格式、命令类型和通信规范

协议规范：
1. 所有通信都通过ttyS4串口进行
2. 收到消息后必须发送通用应答帧（除ACK帧本身）
3. 支持双向通信：N100 ↔ 电源板
4. 消息帧格式固定，便于解析和验证
"""

from enum import IntEnum
from dataclasses import dataclass
from typing import Optional, List
import time


class CommandType(IntEnum):
    """命令类型枚举"""
    # N100 → 电源板的命令
    LED_MODE = 0x01        # LED模式设置命令
    BREATH_PERIOD = 0x02   # 呼吸灯周期设置命令
    SHUTDOWN_SUCCESS = 0x03 # 关机成功消息
    
    # 电源板 → N100的命令
    SHUTDOWN_REQUEST = 0x13 # 关机请求命令
    
    # 双向通用命令
    ACK = 0x80             # 通用应答命令
    
    # 预留扩展命令
    CUSTOM_01 = 0x10       # 自定义命令1
    CUSTOM_02 = 0x11       # 自定义命令2
    CUSTOM_03 = 0x12       # 自定义命令3


class LEDMode(IntEnum):
    """LED模式枚举"""
    NORMAL = 0x00  # 正常模式
    BREATH = 0x01  # 呼吸模式


class BreathPeriod(IntEnum):
    """呼吸周期枚举"""
    PERIOD_1S = 1   # 1秒周期
    PERIOD_3S = 3   # 3秒周期
    PERIOD_5S = 5   # 5秒周期


class FrameConstants:
    """帧常量定义"""
    HEADER = 0xAA      # 帧头
    TAIL = 0x55        # 帧尾
    MIN_LENGTH = 5     # 最小帧长度（帧头+长度+命令+校验+帧尾）
    MAX_LENGTH = 255   # 最大帧长度


@dataclass
class ProtocolFrame:
    """协议帧数据结构"""
    command: int
    data: bytes = b''
    timestamp: float = 0.0
    source: str = 'unknown'
    
    def __post_init__(self):
        if self.timestamp == 0.0:
            self.timestamp = time.time()
    
    @property
    def length(self) -> int:
        """数据长度（命令+数据）"""
        return len(self.data) + 1
    
    def to_bytes(self) -> bytes:
        """转换为字节序列"""
        length = self.length
        checksum_data = bytes([self.command]) + self.data
        checksum = calculate_checksum(checksum_data)
        
        return bytes([
            FrameConstants.HEADER,
            length,
            self.command
        ]) + self.data + bytes([
            checksum,
            FrameConstants.TAIL
        ])
    
    @classmethod
    def from_bytes(cls, data: bytes, source: str = 'unknown') -> Optional['ProtocolFrame']:
        """从字节序列创建帧对象"""
        if not validate_frame(data):
            return None
        
        command = data[2]
        length = data[1]
        frame_data = data[3:3+length-1] if length > 1 else b''
        
        return cls(
            command=command,
            data=frame_data,
            source=source,
            timestamp=time.time()
        )
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"ProtocolFrame(cmd=0x{self.command:02X}, data={self.data.hex(' ').upper()}, src={self.source})"


def calculate_checksum(data: bytes) -> int:
    """
    计算校验和
    使用二进制补码方法: (~sum + 1) & 0xFF
    
    参数:
        data (bytes): 需要计算校验和的数据（命令+数据）
    
    返回:
        int: 校验和
    """
    checksum = sum(data) & 0xFF
    return ((~checksum) + 1) & 0xFF


def validate_frame(frame: bytes) -> bool:
    """
    验证帧的完整性和正确性
    
    参数:
        frame (bytes): 待验证的帧数据
    
    返回:
        bool: 帧是否有效
    """
    # 检查最小长度
    if len(frame) < FrameConstants.MIN_LENGTH:
        return False
    
    # 检查帧头和帧尾
    if frame[0] != FrameConstants.HEADER or frame[-1] != FrameConstants.TAIL:
        return False
    
    # 检查长度字段
    length = frame[1]
    expected_frame_length = 4 + length  # 帧头+长度+数据+校验+帧尾
    if len(frame) != expected_frame_length:
        return False
    
    # 验证校验和
    command = frame[2]
    data = frame[3:3+length-1] if length > 1 else b''
    expected_checksum = calculate_checksum(bytes([command]) + data)
    actual_checksum = frame[3+length-1]
    
    return expected_checksum == actual_checksum


def create_led_mode_frame(mode: LEDMode) -> ProtocolFrame:
    """创建LED模式设置帧"""
    return ProtocolFrame(
        command=CommandType.LED_MODE,
        data=bytes([mode])
    )


def create_breath_period_frame(period: BreathPeriod) -> ProtocolFrame:
    """创建呼吸周期设置帧"""
    return ProtocolFrame(
        command=CommandType.BREATH_PERIOD,
        data=bytes([period])
    )


def create_shutdown_success_frame() -> ProtocolFrame:
    """创建关机成功帧"""
    return ProtocolFrame(
        command=CommandType.SHUTDOWN_SUCCESS
    )


def create_shutdown_request_frame() -> ProtocolFrame:
    """创建关机请求帧"""
    return ProtocolFrame(
        command=CommandType.SHUTDOWN_REQUEST
    )


def create_ack_frame() -> ProtocolFrame:
    """创建通用应答帧"""
    return ProtocolFrame(
        command=CommandType.ACK,
        data=bytes([0x80])  # ACK数据固定为0x80
    )


def create_custom_frame(command: int, data: bytes = b'') -> ProtocolFrame:
    """创建自定义帧"""
    return ProtocolFrame(
        command=command,
        data=data
    )


class ProtocolParser:
    """协议解析器"""
    
    def __init__(self):
        self.buffer = bytearray()
    
    def feed_data(self, data: bytes) -> List[ProtocolFrame]:
        """
        输入数据并解析帧
        
        参数:
            data (bytes): 输入的原始数据
        
        返回:
            List[ProtocolFrame]: 解析出的帧列表
        """
        self.buffer.extend(data)
        frames = []
        
        while len(self.buffer) >= FrameConstants.MIN_LENGTH:
            # 查找帧头
            header_pos = -1
            for i in range(len(self.buffer)):
                if self.buffer[i] == FrameConstants.HEADER:
                    header_pos = i
                    break
            
            if header_pos == -1:
                # 没有找到帧头，清空缓冲区
                self.buffer.clear()
                break
            
            if header_pos > 0:
                # 丢弃帧头前的数据
                self.buffer = self.buffer[header_pos:]
            
            # 检查是否有完整的帧
            if len(self.buffer) < FrameConstants.MIN_LENGTH:
                break
            
            length = self.buffer[1]
            frame_length = 4 + length
            
            if len(self.buffer) < frame_length:
                break
            
            # 提取完整帧
            frame_data = bytes(self.buffer[:frame_length])
            self.buffer = self.buffer[frame_length:]
            
            # 解析帧
            frame = ProtocolFrame.from_bytes(frame_data)
            if frame:
                frames.append(frame)
        
        return frames
    
    def clear_buffer(self):
        """清空缓冲区"""
        self.buffer.clear()


# 预定义的标准帧
STANDARD_FRAMES = {
    'led_normal': create_led_mode_frame(LEDMode.NORMAL),
    'led_breath': create_led_mode_frame(LEDMode.BREATH),
    'breath_1s': create_breath_period_frame(BreathPeriod.PERIOD_1S),
    'breath_3s': create_breath_period_frame(BreathPeriod.PERIOD_3S),
    'breath_5s': create_breath_period_frame(BreathPeriod.PERIOD_5S),
    'shutdown_success': create_shutdown_success_frame(),
    'shutdown_request': create_shutdown_request_frame(),
    'ack': create_ack_frame()
}


def get_standard_frame(name: str) -> Optional[ProtocolFrame]:
    """获取标准帧"""
    return STANDARD_FRAMES.get(name)


def is_ack_required(command: int) -> bool:
    """判断命令是否需要ACK应答"""
    # ACK命令本身不需要应答
    if command == CommandType.ACK:
        return False
    
    # 其他所有命令都需要ACK应答
    return True


def get_command_name(command: int) -> str:
    """获取命令名称"""
    command_names = {
        CommandType.LED_MODE: "LED模式设置",
        CommandType.BREATH_PERIOD: "呼吸周期设置",
        CommandType.SHUTDOWN_SUCCESS: "关机成功",
        CommandType.SHUTDOWN_REQUEST: "关机请求",
        CommandType.ACK: "通用应答",
        CommandType.CUSTOM_01: "自定义命令1",
        CommandType.CUSTOM_02: "自定义命令2",
        CommandType.CUSTOM_03: "自定义命令3"
    }
    return command_names.get(command, f"未知命令(0x{command:02X})")


# 协议版本信息
PROTOCOL_VERSION = "1.0.0"
PROTOCOL_DESCRIPTION = "N100与电源板通信协议"


if __name__ == "__main__":
    # 测试代码
    print(f"协议版本: {PROTOCOL_VERSION}")
    print(f"协议描述: {PROTOCOL_DESCRIPTION}")
    
    # 测试帧创建和解析
    frame = create_led_mode_frame(LEDMode.BREATH)
    frame_bytes = frame.to_bytes()
    print(f"LED呼吸模式帧: {frame_bytes.hex(' ').upper()}")
    
    # 测试解析
    parsed_frame = ProtocolFrame.from_bytes(frame_bytes)
    if parsed_frame:
        print(f"解析结果: {parsed_frame}")
    
    # 测试校验和
    test_data = bytes([0x01, 0x01])  # 命令+数据
    checksum = calculate_checksum(test_data)
    print(f"校验和测试: 数据={test_data.hex(' ').upper()}, 校验和=0x{checksum:02X}")
