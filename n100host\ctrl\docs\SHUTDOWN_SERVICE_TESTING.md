# N100关机服务测试指南

本文档详细说明如何测试N100关机服务的各个组件和完整流程。

## 📋 关机服务组件

### 1. 核心组件
- **n100-shutdown.service** - 系统服务，监听关机请求
- **n100_shutdown_daemon.py** - 关机守护进程
- **shutdown_notify.py** - 关机通知脚本

### 2. 关机流程
1. 电源板发送关机请求 (`AA 01 13 ED 55`)
2. N100收到请求，立即发送ACK应答 (`AA 01 80 80 55`)
3. N100执行关机脚本 (`sudo shutdown -h now`)
4. 系统关机过程中，关机脚本发送关机成功消息 (`AA 01 03 FD 55`)
5. 电源板收到关机成功消息后执行断电

## 🔧 安装关机服务

### 1. 手动安装
```bash
# 复制服务文件
sudo cp services/n100-shutdown.service /etc/systemd/system/

# 重新加载systemd
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable n100-shutdown

# 启动服务
sudo systemctl start n100-shutdown
```

### 2. 自动安装
```bash
# 使用安装脚本
sudo bash scripts/install_optimized_system.sh
```

## 🧪 测试方法

### 1. 服务状态检查

```bash
# 检查服务状态
systemctl status n100-shutdown

# 查看服务日志
journalctl -u n100-shutdown -f

# 检查服务是否启用
systemctl is-enabled n100-shutdown

# 检查服务是否运行
systemctl is-active n100-shutdown
```

### 2. 串口通信测试

```bash
# 检查串口设备
ls -l /dev/ttyS4

# 检查串口权限
sudo chmod 666 /dev/ttyS4

# 检查串口占用
sudo lsof /dev/ttyS4
```

### 3. 模拟关机请求测试

```bash
# 方法1: 使用echo命令发送关机请求
echo -e '\xAA\x01\x13\xED\x55' | sudo tee /dev/ttyS4

# 方法2: 使用Python脚本发送
python3 -c "
import serial
ser = serial.Serial('/dev/ttyS4', 115200, timeout=1)
ser.write(bytes([0xAA, 0x01, 0x13, 0xED, 0x55]))
ser.close()
"

# 方法3: 使用测试工具
python test/integration/test_shutdown_flow.py
```

### 4. 关机成功消息测试

```bash
# 手动发送关机成功消息
python src/power_ctrl_cli.py shutdown

# 使用直接模式
python src/power_ctrl_cli.py --no-manager shutdown

# 测试关机脚本
python test/integration/test_shutdown_script.py
```

## 📊 测试场景

### 场景1: 基本功能测试

```bash
# 1. 启动服务
sudo systemctl start n100-shutdown

# 2. 检查服务状态
systemctl status n100-shutdown

# 3. 发送关机请求
echo -e '\xAA\x01\x13\xED\x55' | sudo tee /dev/ttyS4

# 4. 查看日志
journalctl -u n100-shutdown -n 20
```

**期望结果:**
- 服务正常启动
- 收到关机请求后立即发送ACK
- 执行关机命令
- 日志显示完整流程

### 场景2: 串口冲突测试

```bash
# 1. 检查串口占用
sudo lsof /dev/ttyS4

# 2. 停止可能冲突的服务
sudo systemctl stop n100-serial-manager

# 3. 重启关机服务
sudo systemctl restart n100-shutdown

# 4. 测试关机请求
echo -e '\xAA\x01\x13\xED\x55' | sudo tee /dev/ttyS4
```

### 场景3: 完整关机流程测试

```bash
# 1. 运行完整测试
python test/integration/test_shutdown_flow.py

# 2. 检查所有组件
python test/run_tests.py

# 3. 验证关机脚本
python test/integration/test_shutdown_script.py
```

## 🔍 故障排除

### 1. 服务启动失败

```bash
# 检查服务状态
systemctl status n100-shutdown

# 查看详细错误
journalctl -u n100-shutdown -n 50

# 检查服务文件
sudo systemctl cat n100-shutdown

# 重新加载配置
sudo systemctl daemon-reload
sudo systemctl restart n100-shutdown
```

### 2. 串口权限问题

```bash
# 检查串口权限
ls -l /dev/ttyS4

# 设置权限
sudo chmod 666 /dev/ttyS4

# 添加用户到dialout组
sudo usermod -a -G dialout $USER

# 重新登录或重启
```

### 3. 串口冲突问题

```bash
# 检查占用进程
sudo lsof /dev/ttyS4

# 停止冲突服务
sudo systemctl stop n100-serial-manager

# 使用冲突修复工具
sudo python test/tools/fix_serial_conflict.py
```

### 4. 关机请求无响应

```bash
# 检查守护进程是否运行
ps aux | grep n100_shutdown_daemon

# 检查串口通信
python test/tools/final_diagnosis.py

# 使用直接模式测试
python src/power_ctrl_cli.py --no-manager shutdown
```

## 📝 日志分析

### 正常日志示例

```
[INFO] N100关机守护进程启动
[INFO] 串口 /dev/ttyS4 打开成功
[INFO] 开始监听关机请求...
[INFO] 收到关机请求: AA 01 13 ED 55
[INFO] 发送ACK应答: AA 01 80 80 55
[INFO] 执行关机命令: sudo shutdown -h now
[INFO] 关机流程启动成功
```

### 错误日志示例

```
[ERROR] 串口打开失败: Permission denied
[ERROR] 关机请求处理异常: device disconnected
[WARNING] 未收到完整的关机请求帧
```

## 🚀 性能测试

### 响应时间测试

```bash
# 测试ACK响应时间
time echo -e '\xAA\x01\x13\xED\x55' | sudo tee /dev/ttyS4

# 批量测试
for i in {1..10}; do
    echo "测试 $i"
    echo -e '\xAA\x01\x13\xED\x55' | sudo tee /dev/ttyS4
    sleep 1
done
```

### 并发测试

```bash
# 同时发送多个请求
for i in {1..5}; do
    echo -e '\xAA\x01\x13\xED\x55' | sudo tee /dev/ttyS4 &
done
wait
```

## 📋 测试检查清单

### 安装检查
- [ ] 服务文件已复制到 `/etc/systemd/system/`
- [ ] systemd已重新加载
- [ ] 服务已启用 (`systemctl is-enabled`)
- [ ] 服务正在运行 (`systemctl is-active`)

### 权限检查
- [ ] 串口设备存在 (`/dev/ttyS4`)
- [ ] 串口权限正确 (`666` 或用户在 `dialout` 组)
- [ ] 无其他进程占用串口

### 功能检查
- [ ] 能接收关机请求
- [ ] 能发送ACK应答
- [ ] 能执行关机命令
- [ ] 能发送关机成功消息

### 日志检查
- [ ] 服务启动日志正常
- [ ] 关机请求处理日志正常
- [ ] 无错误或警告日志

## 🎯 自动化测试

### 创建测试脚本

```bash
#!/bin/bash
# test_shutdown_service.sh

echo "=== N100关机服务测试 ==="

# 1. 检查服务状态
echo "1. 检查服务状态..."
systemctl is-active n100-shutdown

# 2. 检查串口
echo "2. 检查串口..."
ls -l /dev/ttyS4

# 3. 发送测试请求
echo "3. 发送关机请求..."
echo -e '\xAA\x01\x13\xED\x55' | sudo tee /dev/ttyS4

# 4. 检查日志
echo "4. 检查最新日志..."
journalctl -u n100-shutdown -n 5 --no-pager

echo "=== 测试完成 ==="
```

### 运行自动化测试

```bash
# 使测试脚本可执行
chmod +x test_shutdown_service.sh

# 运行测试
./test_shutdown_service.sh
```

## 📞 技术支持

如果遇到问题，请按以下顺序排查：

1. **检查服务状态** - `systemctl status n100-shutdown`
2. **查看日志** - `journalctl -u n100-shutdown -f`
3. **检查串口** - `ls -l /dev/ttyS4`
4. **运行诊断工具** - `python test/tools/fix_serial_conflict.py`
5. **使用直接模式** - `python src/power_ctrl_cli.py --no-manager shutdown`

如果问题仍然存在，请提供以下信息：
- 系统版本 (`uname -a`)
- 服务状态 (`systemctl status n100-shutdown`)
- 错误日志 (`journalctl -u n100-shutdown -n 50`)
- 串口状态 (`ls -l /dev/ttyS4`)
