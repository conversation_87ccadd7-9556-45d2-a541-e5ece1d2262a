#!/bin/bash
# N100关机钩子脚本
# 在系统关机时发送关机成功消息给电源板

# 脚本路径
SCRIPT_DIR="/opt/n100/ctrl"
NOTIFY_SCRIPT="$SCRIPT_DIR/shutdown_notify.py"
LOG_FILE="/var/log/n100_shutdown.log"

# 日志函数
log_msg() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# 开始执行
log_msg "关机钩子脚本开始执行"

# 检查通知脚本是否存在
if [ ! -f "$NOTIFY_SCRIPT" ]; then
    log_msg "错误: 找不到关机通知脚本 $NOTIFY_SCRIPT"
    exit 1
fi

# 等待文件系统同步
sync
log_msg "文件系统同步完成"

# 执行关机通知脚本
log_msg "开始发送关机成功消息"
python3 "$NOTIFY_SCRIPT"
exit_code=$?

if [ $exit_code -eq 0 ]; then
    log_msg "关机成功消息发送完成"
else
    log_msg "关机成功消息发送失败，退出码: $exit_code"
fi

log_msg "关机钩子脚本执行完成"
exit 0
