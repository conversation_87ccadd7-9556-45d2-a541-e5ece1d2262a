#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100关机系统测试脚本
测试关机请求监听和关机成功消息发送功能
"""

import os
import sys
import time
import threading
from unittest.mock import MagicMock, patch

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'src'))

# 模拟serial模块
mock_serial = MagicMock()
mock_serial.Serial = MagicMock()
mock_serial.EIGHTBITS = 8
mock_serial.PARITY_NONE = 'N'
mock_serial.STOPBITS_ONE = 1

with patch.dict('sys.modules', {'serial': mock_serial}):
    try:
        from n100_power_ctrl import N100PowerController, PowerCommand
    except ImportError as e:
        print(f"错误: 无法导入n100_power_ctrl模块: {e}")
        sys.exit(1)


def test_shutdown_success_message():
    """测试关机成功消息发送"""
    print("=== 测试关机成功消息发送 ===")
    
    # 模拟串口
    mock_serial = MagicMock()
    mock_serial.Serial = MagicMock()
    mock_serial.EIGHTBITS = 8
    mock_serial.PARITY_NONE = 'N'
    mock_serial.STOPBITS_ONE = 1
    
    with patch.dict('sys.modules', {'serial': mock_serial}):
        controller = N100PowerController()
        
        # 模拟连接成功
        controller.is_connected = True
        controller.serial = MagicMock()
        controller.serial.is_open = True
        controller.serial.in_waiting = 5
        controller.serial.read.return_value = bytes([0xAA, 0x01, 0x80, 0x80, 0x55])
        
        # 测试发送关机成功消息
        print("发送关机成功消息...")
        success = controller.send_shutdown_success()
        
        if success:
            print("✓ 关机成功消息发送测试通过")
            
            # 检查发送的数据
            calls = controller.serial.write.call_args_list
            if calls:
                sent_data = calls[0][0][0]
                expected = bytes([0xAA, 0x01, 0x03, 0xFD, 0x55])
                if sent_data == expected:
                    print(f"✓ 发送的帧格式正确: {sent_data.hex(' ').upper()}")
                else:
                    print(f"✗ 发送的帧格式错误: 期望 {expected.hex(' ').upper()}, 实际 {sent_data.hex(' ').upper()}")
            
            return True
        else:
            print("✗ 关机成功消息发送测试失败")
            return False


def test_shutdown_request_parsing():
    """测试关机请求解析"""
    print("\n=== 测试关机请求解析 ===")
    
    # 模拟串口
    mock_serial = MagicMock()
    mock_serial.Serial = MagicMock()
    mock_serial.EIGHTBITS = 8
    mock_serial.PARITY_NONE = 'N'
    mock_serial.STOPBITS_ONE = 1
    
    with patch.dict('sys.modules', {'serial': mock_serial}):
        controller = N100PowerController()
        
        # 设置回调函数
        shutdown_requested = threading.Event()
        
        def on_shutdown_request():
            print("收到关机请求回调!")
            shutdown_requested.set()
        
        controller.set_shutdown_request_callback(on_shutdown_request)
        
        # 模拟连接成功
        controller.is_connected = True
        controller.serial = MagicMock()
        controller.serial.is_open = True
        
        # 模拟接收关机请求帧
        shutdown_request_frame = bytes([0xAA, 0x01, 0x13, 0xED, 0x55])
        print(f"模拟接收关机请求帧: {shutdown_request_frame.hex(' ').upper()}")
        
        # 将数据添加到接收缓冲区
        controller._rx_buffer.extend(shutdown_request_frame)
        
        # 解析关机请求
        result = controller._parse_shutdown_request()
        
        if result:
            print("✓ 关机请求解析成功")
            
            # 检查是否发送了ACK
            calls = controller.serial.write.call_args_list
            if calls:
                sent_data = calls[0][0][0]
                expected_ack = bytes([0xAA, 0x01, 0x80, 0x80, 0x55])
                if sent_data == expected_ack:
                    print(f"✓ ACK应答发送正确: {sent_data.hex(' ').upper()}")
                else:
                    print(f"✗ ACK应答发送错误: 期望 {expected_ack.hex(' ').upper()}, 实际 {sent_data.hex(' ').upper()}")
            
            return True
        else:
            print("✗ 关机请求解析失败")
            return False


def test_frame_calculation():
    """测试帧格式计算"""
    print("\n=== 测试帧格式计算 ===")
    
    # 测试关机请求帧的校验和计算
    # 命令: 0x13, 校验和应该是 (~0x13 + 1) & 0xFF = 0xEC
    command = 0x13
    expected_checksum = ((~command) + 1) & 0xFF
    
    print(f"关机请求命令: 0x{command:02X}")
    print(f"计算的校验和: 0x{expected_checksum:02X}")
    
    if expected_checksum == 0xED:
        print("✓ 关机请求帧校验和计算正确")
    else:
        print(f"✗ 关机请求帧校验和计算错误，期望 0xED，实际 0x{expected_checksum:02X}")
        return False
    
    # 测试关机成功帧的校验和计算
    command = 0x03
    expected_checksum = ((~command) + 1) & 0xFF
    
    print(f"关机成功命令: 0x{command:02X}")
    print(f"计算的校验和: 0x{expected_checksum:02X}")
    
    if expected_checksum == 0xFD:
        print("✓ 关机成功帧校验和计算正确")
        return True
    else:
        print(f"✗ 关机成功帧校验和计算错误，期望 0xFD，实际 0x{expected_checksum:02X}")
        return False


def test_direct_shutdown_notify():
    """测试直接关机通知脚本"""
    print("\n=== 测试关机通知脚本 ===")

    # 模拟serial模块
    mock_serial_module = MagicMock()
    mock_serial_class = MagicMock()
    mock_serial = MagicMock()
    mock_serial_class.return_value = mock_serial
    mock_serial_module.Serial = mock_serial_class

    with patch.dict('sys.modules', {'serial': mock_serial_module}):
        try:
            from shutdown_notify import send_shutdown_success_direct

            # 测试直接发送
            result = send_shutdown_success_direct('/dev/ttyS4')

            print(f"发送结果: {result}")
            if result:
                print("✓ 关机通知脚本测试通过")

                # 检查发送的数据
                calls = mock_serial.write.call_args_list
                if calls:
                    sent_data = calls[0][0][0]
                    expected = bytes([0xAA, 0x01, 0x03, 0xFD, 0x55])
                    if sent_data == expected:
                        print(f"✓ 发送的帧格式正确: {sent_data.hex(' ').upper()}")
                    else:
                        print(f"✗ 发送的帧格式错误: 期望 {expected.hex(' ').upper()}, 实际 {sent_data.hex(' ').upper()}")

                return True
            else:
                print("✗ 关机通知脚本测试失败")
                return False

        except ImportError as e:
            print(f"✗ 无法导入关机通知脚本: {e}")
            return False


def main():
    """主测试函数"""
    print("N100关机系统测试")
    print("=" * 50)
    
    tests = [
        ("关机成功消息发送", test_shutdown_success_message),
        ("关机请求解析", test_shutdown_request_parsing),
        ("帧格式计算", test_frame_calculation),
        ("关机通知脚本", test_direct_shutdown_notify),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！关机系统可以正常使用。")
        return 0
    else:
        print("❌ 部分测试失败，请检查代码。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
