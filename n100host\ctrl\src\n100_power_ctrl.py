#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100电源控制器串口通信模块
用于N100向电源板发送控制命令并接收应答

消息帧格式：
- 帧头: 0xAA
- 长度: 数据长度(不包括帧头、长度、校验、帧尾)
- 命令: 命令类型
- 数据: 命令数据
- 校验: 校验和(长度+命令+数据的和)
- 帧尾: 0x55

支持的命令：
- 设置LED模式: AA 02 01 00 FF 55 (正常模式) / AA 02 01 01 FE 55 (呼吸模式)
- 设置呼吸周期: AA 02 02 01 FD 55 (1秒) / AA 02 02 03 FB 55 (3秒) / AA 02 02 05 F9 55 (5秒)
- 关机成功: AA 01 03 FD 55
- 关机请求: AA 01 13 ED 55 (电源板发送给N100)
- 通用应答: AA 01 80 80 55
"""

import serial
import time
import threading
import os
import sys
from typing import Optional, Callable
from enum import IntEnum

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from serial_manager import SerialManager, MessageFrame
except ImportError:
    # 如果串口管理器不存在，使用传统模式
    SerialManager = None
    MessageFrame = None


class LEDMode(IntEnum):
    """LED模式枚举"""
    NORMAL = 0x00  # 正常模式
    BREATH = 0x01  # 呼吸模式


class BreathPeriod(IntEnum):
    """呼吸周期枚举"""
    PERIOD_1S = 1   # 1秒周期
    PERIOD_3S = 3   # 3秒周期
    PERIOD_5S = 5   # 5秒周期


class PowerCommand(IntEnum):
    """电源控制命令枚举"""
    LED_MODE = 0x01        # LED模式设置命令
    BREATH_PERIOD = 0x02   # 呼吸灯周期设置命令
    SHUTDOWN = 0x03        # 关机成功消息
    SHUTDOWN_REQ = 0x13    # 关机请求命令（电源板发送给N100）
    ACK = 0x80            # 通用应答命令


class N100PowerController:
    """N100电源控制器类"""

    # 协议常量（向后兼容）
    FRAME_HEADER = 0xAA
    FRAME_TAIL = 0x55

    def __init__(self, port: str = '/dev/ttyS4', baudrate: int = 115200,
                 timeout: float = 1.0, max_retries: int = 10, use_manager: bool = True):
        """
        初始化N100电源控制器

        参数:
            port (str): 串口设备路径，默认'/dev/ttyS4'
            baudrate (int): 波特率，默认115200
            timeout (float): 读取超时时间(秒)，默认1.0
            max_retries (int): 最大重试次数，默认10
            use_manager (bool): 是否使用串口管理器，默认True
        """
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.max_retries = max_retries
        self.use_manager = use_manager and SerialManager is not None
        self.client_id = f"power_ctrl_{int(time.time())}"

        # 传统模式的串口对象
        self.serial = None
        self.is_connected = False

        # 串口管理器模式
        self.manager = None
        self.manager_registered = False

        # 回调函数
        self.on_ack_received: Optional[Callable[[int], None]] = None
        self.on_error: Optional[Callable[[str], None]] = None
        self.on_shutdown_request: Optional[Callable[[], None]] = None

        # 传统模式的接收缓冲区
        self._rx_buffer = bytearray()
        self._rx_lock = threading.Lock()
        
    def connect(self) -> bool:
        """
        连接串口

        返回:
            bool: 连接是否成功
        """
        if self.use_manager:
            return self._connect_with_manager()
        else:
            return self._connect_direct()

    def _connect_with_manager(self) -> bool:
        """通过串口管理器连接"""
        try:
            from serial_manager import get_serial_manager

            self.manager = get_serial_manager()

            # 启动管理器（如果尚未启动）
            if not self.manager.is_running:
                if not self.manager.start():
                    print(f"[错误] 无法启动串口管理器")
                    return False

            # 注册客户端
            success = self.manager.register_client(
                client_id=self.client_id,
                message_callback=self._on_message_received,
                error_callback=self._on_error_received
            )

            if success:
                self.manager_registered = True
                self.is_connected = True
                print(f"[连接] 已通过串口管理器连接到 {self.port}")
                return True
            else:
                print(f"[错误] 无法注册到串口管理器")
                return False

        except Exception as e:
            print(f"[错误] 串口管理器连接失败: {e}")
            return False

    def _connect_direct(self) -> bool:
        """直接连接串口（传统模式）"""
        try:
            self.serial = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=self.timeout
            )
            self.is_connected = True
            print(f"[连接] 成功直接连接到 {self.port}")
            return True
        except Exception as e:
            print(f"[错误] 直接连接失败: {e}")
            self.is_connected = False
            return False

    def disconnect(self):
        """断开串口连接"""
        if self.use_manager and self.manager_registered:
            self.manager.unregister_client(self.client_id)
            self.manager_registered = False
            print("[断开] 已从串口管理器断开")
        elif self.serial and self.serial.is_open:
            self.serial.close()
            print("[断开] 串口已断开")

        self.is_connected = False

    def _on_message_received(self, message):
        """串口管理器消息回调"""
        if MessageFrame and isinstance(message, MessageFrame):
            print(f"[接收] 收到消息: 命令=0x{message.command:02X}, 数据={message.data.hex(' ').upper()}")

            # 处理关机请求
            if message.command == PowerCommand.SHUTDOWN_REQ:
                print("[关机] 收到电源板关机请求")
                if self.on_shutdown_request:
                    self.on_shutdown_request()

            # 处理ACK应答
            elif message.command == PowerCommand.ACK:
                print("[ACK] 收到通用应答")
                if self.on_ack_received:
                    self.on_ack_received(message.command)

    def _on_error_received(self, error_msg: str):
        """串口管理器错误回调"""
        print(f"[错误] 串口管理器错误: {error_msg}")
        if self.on_error:
            self.on_error(error_msg)

    def _calculate_checksum(self, data: bytes) -> int:
        """
        计算校验和

        参数:
            data (bytes): 需要计算校验和的数据(长度+命令+数据)

        返回:
            int: 校验和，使用二进制补码方法 (~sum + 1)
        """
        # 根据电源板端代码: return (~checksum + 1);
        # 这里data应该只包含命令+数据，不包含长度
        checksum = sum(data) & 0xFF
        return ((~checksum) + 1) & 0xFF
    
    def _create_frame(self, command: int, data: bytes = b'') -> bytes:
        """
        创建消息帧

        参数:
            command (int): 命令字节
            data (bytes): 数据字节

        返回:
            bytes: 完整的消息帧
        """
        length = len(data) + 1  # 数据长度 + 命令长度

        # 校验和只对命令+数据计算，不包含长度
        checksum_data = bytes([command]) + data
        checksum = self._calculate_checksum(checksum_data)

        frame = bytes([self.FRAME_HEADER, length, command]) + data + bytes([checksum, self.FRAME_TAIL])
        return frame
    
    def _send_frame_with_retry(self, frame: bytes, command_name: str = "") -> bool:
        """
        发送帧并等待ACK应答，支持重试
        
        参数:
            frame (bytes): 要发送的帧
            command_name (str): 命令名称(用于日志)
            
        返回:
            bool: 是否发送成功
        """
        if not self.is_connected or not self.serial:
            print("[错误] 串口未连接")
            return False
        
        for attempt in range(self.max_retries):
            try:
                # 清空输入缓冲区
                self.serial.reset_input_buffer()
                
                # 发送帧
                print(f"[发送] {command_name} 帧 (尝试 {attempt + 1}/{self.max_retries}): {frame.hex(' ').upper()}")
                self.serial.write(frame)
                self.serial.flush()
                
                # 等待ACK应答
                if self._wait_for_ack():
                    print(f"[成功] {command_name} 命令发送成功")
                    return True
                else:
                    print(f"[重试] {command_name} 未收到ACK应答，准备重试...")
                    
            except Exception as e:
                print(f"[错误] {command_name} 发送异常: {e}")
                if self.on_error:
                    self.on_error(f"{command_name} 发送异常: {e}")
        
        print(f"[失败] {command_name} 在 {self.max_retries} 次尝试后仍未成功")
        return False
    
    def _wait_for_ack(self) -> bool:
        """
        等待ACK应答帧
        
        返回:
            bool: 是否收到有效的ACK应答
        """
        start_time = time.time()
        
        while time.time() - start_time < self.timeout:
            if self.serial.in_waiting > 0:
                data = self.serial.read(self.serial.in_waiting)
                print(f"[接收] 原始数据: {data.hex(' ').upper()}")
                
                with self._rx_lock:
                    self._rx_buffer.extend(data)
                    
                    # 尝试解析ACK帧
                    if self._parse_ack_frame():
                        return True
            
            time.sleep(0.01)  # 短暂休眠避免CPU占用过高
        
        return False
    
    def _parse_ack_frame(self) -> bool:
        """
        解析ACK应答帧
        期望的ACK帧: AA 01 80 80 55
        
        返回:
            bool: 是否解析到有效的ACK帧
        """
        expected_ack = bytes([0xAA, 0x01, 0x80, 0x80, 0x55])
        
        # 在缓冲区中查找ACK帧
        while len(self._rx_buffer) >= len(expected_ack):
            # 查找帧头
            try:
                header_pos = self._rx_buffer.index(self.FRAME_HEADER)
                if header_pos > 0:
                    # 丢弃帧头前的数据
                    self._rx_buffer = self._rx_buffer[header_pos:]
                
                # 检查是否有完整的ACK帧
                if len(self._rx_buffer) >= len(expected_ack):
                    potential_ack = bytes(self._rx_buffer[:len(expected_ack)])
                    
                    if potential_ack == expected_ack:
                        print("[接收] 收到有效ACK应答: AA 01 80 80 55")
                        # 移除已处理的ACK帧
                        self._rx_buffer = self._rx_buffer[len(expected_ack):]
                        
                        if self.on_ack_received:
                            self.on_ack_received(PowerCommand.ACK)
                        
                        return True
                    else:
                        # 不是有效的ACK帧，移除第一个字节继续查找
                        self._rx_buffer = self._rx_buffer[1:]
                else:
                    # 数据不够，等待更多数据
                    break
                    
            except ValueError:
                # 没有找到帧头，清空缓冲区
                self._rx_buffer.clear()
                break
        
        return False

    def _parse_shutdown_request(self) -> bool:
        """
        解析关机请求帧
        期望的关机请求帧: AA 01 13 EC 55

        返回:
            bool: 是否解析到有效的关机请求帧
        """
        # 计算关机请求帧的校验和
        # 命令: 0x13, 校验和 = (~0x13 + 1) & 0xFF = 0xED
        expected_shutdown_req = bytes([0xAA, 0x01, 0x13, 0xED, 0x55])

        # 在缓冲区中查找关机请求帧
        while len(self._rx_buffer) >= len(expected_shutdown_req):
            # 查找帧头
            try:
                header_pos = self._rx_buffer.index(self.FRAME_HEADER)
                if header_pos > 0:
                    # 丢弃帧头前的数据
                    self._rx_buffer = self._rx_buffer[header_pos:]

                # 检查是否有完整的关机请求帧
                if len(self._rx_buffer) >= len(expected_shutdown_req):
                    potential_frame = bytes(self._rx_buffer[:len(expected_shutdown_req)])

                    if potential_frame == expected_shutdown_req:
                        print("[接收] 收到关机请求: AA 01 13 EC 55")
                        # 移除已处理的关机请求帧
                        self._rx_buffer = self._rx_buffer[len(expected_shutdown_req):]

                        # 立即发送ACK应答
                        self._send_ack_immediately()

                        # 调用关机请求回调
                        if self.on_shutdown_request:
                            self.on_shutdown_request()

                        return True
                    else:
                        # 不是有效的关机请求帧，移除第一个字节继续查找
                        self._rx_buffer = self._rx_buffer[1:]
                else:
                    # 数据不够，等待更多数据
                    break

            except ValueError:
                # 没有找到帧头，清空缓冲区
                self._rx_buffer.clear()
                break

        return False

    def _send_ack_immediately(self):
        """立即发送ACK应答（用于关机请求）"""
        try:
            ack_frame = bytes([0xAA, 0x01, 0x80, 0x80, 0x55])
            self.serial.write(ack_frame)
            self.serial.flush()
            print("[发送] 已发送ACK应答: AA 01 80 80 55")
        except Exception as e:
            print(f"[错误] 发送ACK应答失败: {e}")

    # ========================================================================
    #                              公共接口方法
    # ========================================================================

    def set_led_mode(self, mode: LEDMode) -> bool:
        """
        设置LED模式

        参数:
            mode (LEDMode): LED模式 (NORMAL=0x00, BREATH=0x01)

        返回:
            bool: 是否设置成功
        """
        data = bytes([mode])
        mode_name = "正常模式" if mode == LEDMode.NORMAL else "呼吸模式"

        if self.use_manager and self.manager_registered:
            return self.manager.send_message(
                client_id=self.client_id,
                command=PowerCommand.LED_MODE,
                data=data,
                max_retries=self.max_retries
            )
        else:
            frame = self._create_frame(PowerCommand.LED_MODE, data)
            return self._send_frame_with_retry(frame, f"设置LED模式({mode_name})")

    def set_breath_period(self, period: BreathPeriod) -> bool:
        """
        设置呼吸灯周期

        参数:
            period (BreathPeriod): 呼吸周期 (1, 3, 5秒)

        返回:
            bool: 是否设置成功
        """
        data = bytes([period])

        if self.use_manager and self.manager_registered:
            return self.manager.send_message(
                client_id=self.client_id,
                command=PowerCommand.BREATH_PERIOD,
                data=data,
                max_retries=self.max_retries
            )
        else:
            frame = self._create_frame(PowerCommand.BREATH_PERIOD, data)
            return self._send_frame_with_retry(frame, f"设置呼吸周期({period}秒)")

    def send_shutdown_success(self) -> bool:
        """
        发送关机成功消息

        返回:
            bool: 是否发送成功
        """
        if self.use_manager and self.manager_registered:
            return self.manager.send_message(
                client_id=self.client_id,
                command=PowerCommand.SHUTDOWN,
                data=b'',
                max_retries=self.max_retries
            )
        else:
            frame = self._create_frame(PowerCommand.SHUTDOWN)
            return self._send_frame_with_retry(frame, "关机成功")

    def send_custom_command(self, command: int, data: bytes = b'') -> bool:
        """
        发送自定义命令

        参数:
            command (int): 命令字节
            data (bytes): 数据字节

        返回:
            bool: 是否发送成功
        """
        if self.use_manager and self.manager_registered:
            return self.manager.send_message(
                client_id=self.client_id,
                command=command,
                data=data,
                max_retries=self.max_retries
            )
        else:
            frame = self._create_frame(command, data)
            return self._send_frame_with_retry(frame, f"自定义命令(0x{command:02X})")

    # ========================================================================
    #                              便捷方法
    # ========================================================================

    def set_led_normal(self) -> bool:
        """设置LED为正常模式"""
        return self.set_led_mode(LEDMode.NORMAL)

    def set_led_breath(self) -> bool:
        """设置LED为呼吸模式"""
        return self.set_led_mode(LEDMode.BREATH)

    def set_breath_1s(self) -> bool:
        """设置呼吸周期为1秒"""
        return self.set_breath_period(BreathPeriod.PERIOD_1S)

    def set_breath_3s(self) -> bool:
        """设置呼吸周期为3秒"""
        return self.set_breath_period(BreathPeriod.PERIOD_3S)

    def set_breath_5s(self) -> bool:
        """设置呼吸周期为5秒"""
        return self.set_breath_period(BreathPeriod.PERIOD_5S)

    # ========================================================================
    #                              回调设置方法
    # ========================================================================

    def set_ack_callback(self, callback: Callable[[int], None]):
        """
        设置ACK接收回调函数

        参数:
            callback: 回调函数，参数为命令类型
        """
        self.on_ack_received = callback

    def set_error_callback(self, callback: Callable[[str], None]):
        """
        设置错误回调函数

        参数:
            callback: 回调函数，参数为错误信息
        """
        self.on_error = callback

    def set_shutdown_request_callback(self, callback: Callable[[], None]):
        """
        设置关机请求回调函数

        参数:
            callback: 回调函数，无参数
        """
        self.on_shutdown_request = callback

    # ========================================================================
    #                              监听方法
    # ========================================================================

    def monitor_shutdown_requests(self, timeout: float = None) -> bool:
        """
        监听关机请求

        参数:
            timeout (float): 监听超时时间(秒)，None表示无限等待

        返回:
            bool: 是否收到关机请求
        """
        if not self.is_connected or not self.serial:
            print("[错误] 串口未连接")
            return False

        print("[监听] 开始监听关机请求...")
        start_time = time.time()

        while True:
            # 检查超时
            if timeout is not None and time.time() - start_time > timeout:
                print("[超时] 监听关机请求超时")
                return False

            try:
                # 检查是否有数据可读
                if self.serial.in_waiting > 0:
                    data = self.serial.read(self.serial.in_waiting)
                    print(f"[接收] 原始数据: {data.hex(' ').upper()}")

                    with self._rx_lock:
                        self._rx_buffer.extend(data)

                        # 尝试解析关机请求帧
                        if self._parse_shutdown_request():
                            return True

                        # 也检查是否有其他帧（如ACK）
                        self._parse_ack_frame()

                # 短暂休眠避免CPU占用过高
                time.sleep(0.01)

            except Exception as e:
                print(f"[错误] 监听过程中发生异常: {e}")
                if self.on_error:
                    self.on_error(f"监听异常: {e}")
                time.sleep(0.1)

    # ========================================================================
    #                              状态查询方法
    # ========================================================================

    def is_port_connected(self) -> bool:
        """检查串口是否已连接"""
        return self.is_connected and self.serial and self.serial.is_open

    def get_port_info(self) -> dict:
        """获取串口信息"""
        return {
            'port': self.port,
            'baudrate': self.baudrate,
            'timeout': self.timeout,
            'max_retries': self.max_retries,
            'connected': self.is_port_connected()
        }
