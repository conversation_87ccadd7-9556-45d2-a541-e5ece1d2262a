#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
通信测试脚本
使用虚拟串口测试N100与电源板的通信
"""

import os
import sys
import time
import threading
import subprocess
from typing import Optional

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from n100_power_ctrl import N100PowerController, LEDMode, BreathPeriod
    from power_board_simulator import PowerBoardSimulator
    from protocol import CommandType
except ImportError as e:
    print(f"错误: 无法导入必要模块: {e}")
    sys.exit(1)


class CommunicationTester:
    """通信测试器"""
    
    def __init__(self):
        self.n100_port = '/tmp/ttyS4_n100'
        self.power_port = '/tmp/ttyS4_power'
        self.simulator = None
        self.simulator_thread = None
        
    def setup_virtual_serial(self) -> bool:
        """设置虚拟串口"""
        print("=== 设置虚拟串口 ===")
        
        try:
            # 检查socat是否可用
            result = subprocess.run(['which', 'socat'], capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ socat未安装，请先安装: sudo apt-get install socat")
                return False
            
            # 创建虚拟串口对
            cmd = [
                'socat', 
                f'pty,raw,echo=0,link={self.n100_port}',
                f'pty,raw,echo=0,link={self.power_port}'
            ]
            
            print("创建虚拟串口对...")
            self.socat_process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # 等待设备创建
            time.sleep(2)
            
            # 检查设备是否创建成功
            if os.path.exists(self.n100_port) and os.path.exists(self.power_port):
                print(f"✅ 虚拟串口创建成功:")
                print(f"   N100端: {self.n100_port}")
                print(f"   电源板端: {self.power_port}")
                return True
            else:
                print("❌ 虚拟串口创建失败")
                return False
                
        except Exception as e:
            print(f"❌ 设置虚拟串口失败: {e}")
            return False
    
    def start_power_board_simulator(self) -> bool:
        """启动电源板模拟器"""
        print("\n=== 启动电源板模拟器 ===")
        
        try:
            self.simulator = PowerBoardSimulator(port=self.power_port)
            
            if self.simulator.start():
                print("✅ 电源板模拟器启动成功")
                return True
            else:
                print("❌ 电源板模拟器启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 启动电源板模拟器失败: {e}")
            return False
    
    def test_n100_communication(self) -> bool:
        """测试N100通信"""
        print("\n=== 测试N100通信 ===")
        
        try:
            # 创建N100控制器（不使用串口管理器）
            controller = N100PowerController(
                port=self.n100_port,
                use_manager=False,
                max_retries=3,
                timeout=2.0
            )
            
            print("连接N100控制器...")
            if not controller.connect():
                print("❌ N100控制器连接失败")
                return False
            
            print("✅ N100控制器连接成功")
            
            # 测试LED控制
            print("\n1. 测试LED正常模式...")
            success = controller.set_led_mode(LEDMode.NORMAL)
            print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")
            
            if success:
                time.sleep(1)
                
                print("\n2. 测试LED呼吸模式...")
                success = controller.set_led_mode(LEDMode.BREATH)
                print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")
                
                if success:
                    time.sleep(1)
                    
                    print("\n3. 测试呼吸周期设置...")
                    success = controller.set_breath_period(BreathPeriod.PERIOD_3S)
                    print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")
                    
                    if success:
                        time.sleep(1)
                        
                        print("\n4. 测试关机成功消息...")
                        success = controller.send_shutdown_success()
                        print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")
            
            controller.disconnect()
            print("N100控制器已断开")
            
            return success
            
        except Exception as e:
            print(f"❌ N100通信测试失败: {e}")
            return False
    
    def test_shutdown_request(self) -> bool:
        """测试关机请求"""
        print("\n=== 测试关机请求 ===")
        
        if not self.simulator:
            print("❌ 电源板模拟器未启动")
            return False
        
        try:
            print("发送关机请求...")
            self.simulator.send_shutdown_request()
            
            # 等待一段时间观察N100的响应
            time.sleep(2)
            
            print("✅ 关机请求已发送")
            return True
            
        except Exception as e:
            print(f"❌ 关机请求测试失败: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        print("\n=== 清理资源 ===")
        
        # 停止电源板模拟器
        if self.simulator:
            self.simulator.stop()
        
        # 停止socat进程
        if hasattr(self, 'socat_process'):
            self.socat_process.terminate()
            self.socat_process.wait()
        
        # 删除虚拟串口文件
        for port in [self.n100_port, self.power_port]:
            if os.path.exists(port):
                try:
                    os.unlink(port)
                except:
                    pass
        
        print("✅ 资源清理完成")
    
    def run_full_test(self):
        """运行完整测试"""
        print("N100通信完整测试")
        print("=" * 40)
        
        try:
            # 1. 设置虚拟串口
            if not self.setup_virtual_serial():
                return False
            
            # 2. 启动电源板模拟器
            if not self.start_power_board_simulator():
                return False
            
            # 3. 测试N100通信
            comm_success = self.test_n100_communication()
            
            # 4. 测试关机请求
            shutdown_success = self.test_shutdown_request()
            
            # 5. 显示测试结果
            print(f"\n=== 测试结果总结 ===")
            print(f"N100通信测试: {'✅ 通过' if comm_success else '❌ 失败'}")
            print(f"关机请求测试: {'✅ 通过' if shutdown_success else '❌ 失败'}")
            
            if comm_success and shutdown_success:
                print("\n🎉 所有测试通过！通信功能正常")
                return True
            else:
                print("\n❌ 部分测试失败，需要检查问题")
                return False
                
        except KeyboardInterrupt:
            print("\n用户中断测试")
            return False
        except Exception as e:
            print(f"\n❌ 测试异常: {e}")
            return False
        finally:
            self.cleanup()


def main():
    """主函数"""
    tester = CommunicationTester()
    
    try:
        success = tester.run_full_test()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被中断")
        tester.cleanup()
        sys.exit(1)


if __name__ == "__main__":
    main()
