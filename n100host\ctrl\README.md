# N100电源控制系统

## 📋 项目概述

N100电源控制系统实现了N100主板与电源板之间的串口通信，支持LED控制、呼吸灯设置和关机管理功能。

## ✨ 主要功能

- **LED模式控制**: 正常模式/呼吸模式切换
- **呼吸周期设置**: 支持1秒/3秒/5秒周期
- **关机成功通知**: 向电源板发送关机完成消息
- **关机请求监听**: 实时监听电源板的关机请求
- **高可靠性**: 默认10次重试机制

## 📁 目录结构

```
n100host/ctrl/
├── src/                          # 核心源代码
│   ├── n100_power_ctrl.py       # 电源控制器核心类
│   ├── power_ctrl_cli.py        # 命令行工具
│   ├── n100_shutdown_daemon.py  # 关机守护进程
│   └── shutdown_notify.py       # 关机通知脚本
├── scripts/                     # 安装脚本
│   ├── install_shutdown_system.sh
│   └── n100-shutdown-hook.sh
├── services/                    # 系统服务
│   └── n100-shutdown.service
├── tests/                       # 测试文件
│   ├── test_shutdown_system.py
│   └── test_syntax.py
├── examples/                    # 示例代码
│   └── example.py
└── docs/                        # 文档
    ├── README.md
    └── USAGE.md
```

## 🚀 快速开始

### 1. 安装依赖
```bash
pip3 install pyserial
```

### 2. 基本使用
```bash
# LED控制
python3 src/power_ctrl_cli.py led normal    # 正常模式
python3 src/power_ctrl_cli.py led breath    # 呼吸模式

# 呼吸周期设置
python3 src/power_ctrl_cli.py breath 3      # 3秒周期

# 关机成功通知
python3 src/power_ctrl_cli.py shutdown

# 运行测试
python3 src/power_ctrl_cli.py test
```

### 3. 使用Makefile
```bash
make led-breath    # 设置LED呼吸模式
make breath-3s     # 设置3秒呼吸周期
make test          # 运行测试
make example       # 运行示例
```

## 🔧 消息协议

| 功能 | 命令 | 消息帧 | 说明 |
|------|------|--------|------|
| LED正常模式 | 0x01 | `AA 02 01 00 FF 55` | 设置LED正常模式 |
| LED呼吸模式 | 0x01 | `AA 02 01 01 FE 55` | 设置LED呼吸模式 |
| 1秒呼吸周期 | 0x02 | `AA 02 02 01 FD 55` | 设置1秒呼吸周期 |
| 3秒呼吸周期 | 0x02 | `AA 02 02 03 FB 55` | 设置3秒呼吸周期 |
| 5秒呼吸周期 | 0x02 | `AA 02 02 05 F9 55` | 设置5秒呼吸周期 |
| 关机成功 | 0x03 | `AA 01 03 FD 55` | 关机成功消息 |
| 关机请求 | 0x13 | `AA 01 13 ED 55` | 电源板发送的关机请求 |
| 通用应答 | 0x80 | `AA 01 80 80 55` | ACK应答帧 |

## 🛠️ Python API

```python
from src.n100_power_ctrl import N100PowerController

# 创建控制器
controller = N100PowerController(port='/dev/ttyS4')

# 连接并控制
if controller.connect():
    controller.set_led_breath()         # 设置呼吸模式
    controller.set_breath_3s()          # 3秒周期
    controller.send_shutdown_success()  # 关机成功
    controller.disconnect()
```

## 🔄 关机系统

### 安装关机系统
```bash
sudo scripts/install_shutdown_system.sh
```

### 管理服务
```bash
systemctl start n100-shutdown.service
systemctl status n100-shutdown.service
systemctl stop n100-shutdown.service
```

### 查看日志
```bash
tail -f /var/log/n100_shutdown.log
```

## 📊 测试验证

```bash
# 语法和基本功能测试
python3 tests/test_syntax.py

# 关机系统测试
python3 tests/test_shutdown_system.py

# 使用示例测试
python3 examples/example.py
```

## 🔧 配置要求

### 硬件要求
- N100主板
- 电源板（支持串口通信）
- 串口连接（ttyS4）

### 软件要求
- Ubuntu/Linux系统
- Python 3.6+
- pyserial库

### 默认配置参数
- 串口设备: `/dev/ttyS4`
- 波特率: `115200`
- 超时时间: `1.0秒`
- 最大重试次数: `10次`

## 🚨 故障排除

### 常见问题
1. **串口权限不足**: `sudo chmod 666 /dev/ttyS4`
2. **Python模块导入失败**: `pip3 install pyserial`
3. **服务启动失败**: `systemctl status n100-shutdown.service`

### 权限配置
```bash
# 设置串口权限
sudo chmod 666 /dev/ttyS4

# 添加用户到dialout组
sudo usermod -a -G dialout $USER
```

## 📖 详细文档

- **[docs/README.md](docs/README.md)** - 电源控制器详细说明
- **[docs/USAGE.md](docs/USAGE.md)** - 使用指南
- **[README_SHUTDOWN.md](README_SHUTDOWN.md)** - 关机系统说明

## 📄 许可证

本项目采用MIT许可证。
