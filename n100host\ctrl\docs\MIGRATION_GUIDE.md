# N100电源控制系统迁移指南

## 📋 概述

本指南帮助您从原版本的N100电源控制系统迁移到优化版本。优化版本解决了串口冲突问题，提供了更好的可扩展性和稳定性。

## 🔄 迁移步骤

### 1. 备份现有系统

```bash
# 停止现有服务
sudo systemctl stop n100-shutdown.service

# 备份配置文件
sudo cp -r /opt/n100 /opt/n100_backup_$(date +%Y%m%d)

# 备份日志文件
sudo cp /var/log/n100_shutdown.log /var/log/n100_shutdown_backup_$(date +%Y%m%d).log
```

### 2. 安装优化版本

```bash
# 运行优化版本安装脚本
sudo scripts/install_optimized_system.sh
```

### 3. 验证迁移

```bash
# 检查新服务状态
systemctl status n100-serial-manager
systemctl status n100-shutdown

# 测试基本功能
python3 src/power_ctrl_cli.py test

# 运行优化版本示例
python3 examples/optimized_example.py
```

## 🔧 主要变化

### 架构变化

| 组件 | 原版本 | 优化版本 | 变化说明 |
|------|--------|----------|----------|
| 串口访问 | 直接访问 | 通过管理器 | 统一管理，避免冲突 |
| 通信方式 | 点对点 | 多客户端 | 支持多程序同时使用 |
| 应答处理 | 手动 | 自动 | 自动处理ACK帧 |
| 服务依赖 | 独立 | 分层 | 关机服务依赖串口管理器 |

### 新增组件

1. **串口管理器** (`serial_manager.py`)
   - 统一管理ttyS4串口访问
   - 支持多客户端注册
   - 自动处理通用应答帧

2. **通信协议模块** (`protocol.py`)
   - 标准化消息帧格式
   - 统一命令定义
   - 帧验证和解析

3. **串口管理器服务** (`n100-serial-manager.service`)
   - 系统级串口管理服务
   - 自动启动和恢复
   - 为其他服务提供基础

### 优化组件

1. **电源控制器** (`n100_power_ctrl.py`)
   - 支持串口管理器模式
   - 保持向后兼容
   - 改进错误处理

2. **关机守护进程** (`n100_shutdown_daemon.py`)
   - 通过串口管理器通信
   - 简化关机流程
   - 更好的状态管理

3. **关机通知脚本** (`shutdown_notify.py`)
   - 多种发送方式
   - 优先使用串口管理器
   - 改进错误恢复

## 📝 API兼容性

### 保持兼容的API

```python
# 这些API调用在优化版本中保持不变
from n100_power_ctrl import N100PowerController, LEDMode, BreathPeriod

controller = N100PowerController()
controller.connect()
controller.set_led_mode(LEDMode.BREATH)
controller.set_breath_period(BreathPeriod.PERIOD_3S)
controller.send_shutdown_success()
controller.disconnect()
```

### 新增API

```python
# 新的串口管理器API
from serial_manager import get_serial_manager, MessageFrame
from protocol import CommandType, create_led_mode_frame

# 获取串口管理器
manager = get_serial_manager()

# 注册客户端
manager.register_client('my_app', message_callback, error_callback)

# 发送消息
manager.send_message('my_app', CommandType.LED_MODE, bytes([1]))

# 注销客户端
manager.unregister_client('my_app')
```

## 🔧 配置迁移

### 服务配置

原版本的服务配置会被自动更新：

```ini
# 原版本 n100-shutdown.service
[Unit]
Description=N100 Shutdown Daemon
After=network.target

[Service]
ExecStart=/usr/bin/python3 /opt/n100/ctrl/n100_shutdown_daemon.py
# Restart=always  # 被注释掉避免串口冲突

[Install]
# WantedBy=multi-user.target  # 被注释掉手动启动
```

```ini
# 优化版本 n100-shutdown.service
[Unit]
Description=N100 Shutdown Daemon
After=network.target n100-serial-manager.service
Requires=n100-serial-manager.service

[Service]
ExecStart=/usr/bin/python3 /opt/n100/ctrl/n100_shutdown_daemon.py
Restart=always  # 恢复自动重启

[Install]
WantedBy=multi-user.target  # 恢复自动启动
```

### 日志配置

优化版本增加了新的日志文件：

```bash
# 原版本日志
/var/log/n100_shutdown.log

# 优化版本日志
/var/log/n100_shutdown.log        # 关机守护进程日志
/var/log/n100_serial_manager.log  # 串口管理器日志（新增）
```

## 🚨 注意事项

### 1. 服务启动顺序

优化版本中，关机守护进程依赖串口管理器：

```bash
# 正确的启动顺序
systemctl start n100-serial-manager  # 先启动串口管理器
systemctl start n100-shutdown        # 再启动关机守护进程

# 自动处理依赖关系
systemctl start n100-shutdown        # 会自动启动依赖的串口管理器
```

### 2. 串口访问权限

优化版本统一管理串口权限：

```bash
# 检查串口权限
ls -l /dev/ttyS4

# 如果权限不正确，运行安装脚本会自动修复
sudo scripts/install_optimized_system.sh
```

### 3. 多程序使用

优化版本支持多个程序同时使用串口：

```python
# 多个程序可以同时注册到串口管理器
# 程序A
controller_a = N100PowerController(use_manager=True)
controller_a.connect()

# 程序B
controller_b = N100PowerController(use_manager=True)
controller_b.connect()

# 两个程序可以同时工作，不会冲突
```

## 🔍 故障排除

### 迁移后常见问题

1. **串口管理器启动失败**
   ```bash
   # 检查串口权限
   sudo chmod 666 /dev/ttyS4
   
   # 重启服务
   sudo systemctl restart n100-serial-manager
   ```

2. **关机守护进程无法连接**
   ```bash
   # 检查串口管理器状态
   systemctl status n100-serial-manager
   
   # 查看详细日志
   journalctl -u n100-serial-manager -f
   ```

3. **原有程序无法工作**
   ```bash
   # 确保使用管理器模式
   # 在代码中设置 use_manager=True
   controller = N100PowerController(use_manager=True)
   ```

### 回滚到原版本

如果需要回滚到原版本：

```bash
# 停止优化版本服务
sudo systemctl stop n100-shutdown n100-serial-manager
sudo systemctl disable n100-shutdown n100-serial-manager

# 恢复备份
sudo rm -rf /opt/n100
sudo mv /opt/n100_backup_YYYYMMDD /opt/n100

# 重新安装原版本
sudo scripts/install_shutdown_system.sh
```

## 📈 迁移验证清单

- [ ] 备份原系统完成
- [ ] 优化版本安装成功
- [ ] 串口管理器服务正常运行
- [ ] 关机守护进程服务正常运行
- [ ] LED控制功能正常
- [ ] 呼吸灯控制功能正常
- [ ] 关机成功消息发送正常
- [ ] 多程序并发测试通过
- [ ] 日志记录正常
- [ ] 原有程序兼容性验证通过

## 📞 技术支持

如果在迁移过程中遇到问题，请：

1. 查看详细日志：`journalctl -u n100-serial-manager -u n100-shutdown`
2. 运行测试脚本：`python3 tests/test_optimized_system.py`
3. 检查系统状态：`systemctl status n100-*`

---

**建议**: 在生产环境迁移前，请在测试环境中完整验证迁移流程。
