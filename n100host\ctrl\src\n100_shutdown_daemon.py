#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100关机守护进程 - 优化版本
监听电源板发送的关机请求，执行关机流程，并在文件系统关闭后发送关机成功消息

优化功能：
1. 使用串口管理器统一管理ttyS4通信
2. 自动处理通用应答帧
3. 简化关机流程和状态检测
4. 支持可扩展的串口通信架构
5. 改进的Linux关机状态检测
"""

import os
import sys
import time
import signal
import threading
import subprocess
import logging
from pathlib import Path
from typing import Optional

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from serial_manager import get_serial_manager, MessageFrame, MessageType
    from n100_power_ctrl import N100PowerController, PowerCommand
    from protocol import CommandType, create_shutdown_success_frame
except ImportError as e:
    print(f"错误: 无法导入必要模块: {e}")
    print("请确保所有依赖模块都在同一目录下")
    sys.exit(1)


class ShutdownCommand:
    """关机请求命令定义"""
    SHUTDOWN_REQ = 0x13  # 电源板发送的关机请求命令


class N100ShutdownDaemon:
    """N100关机守护进程类 - 优化版本"""

    def __init__(self, port: str = '/dev/ttyS4', log_file: str = '/var/log/n100_shutdown.log'):
        """
        初始化关机守护进程

        参数:
            port (str): 串口设备路径
            log_file (str): 日志文件路径
        """
        self.port = port
        self.log_file = log_file
        self.running = False
        self.shutdown_requested = False
        self.client_id = f"shutdown_daemon_{int(time.time())}"

        # 串口管理器
        self.manager = None
        self.manager_registered = False

        # 设置日志
        self._setup_logging()

        # 注册信号处理器
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)

        self.logger.info("N100关机守护进程初始化完成（优化版本）")
    
    def _setup_logging(self):
        """设置日志记录"""
        # 确保日志目录存在
        log_dir = os.path.dirname(self.log_file)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)
        
        # 配置日志格式
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger('N100ShutdownDaemon')
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，准备退出...")
        self.stop()
    
    def start(self):
        """启动守护进程"""
        self.logger.info("启动N100关机守护进程（优化版本）...")

        try:
            # 获取串口管理器
            self.manager = get_serial_manager()

            # 启动串口管理器（如果尚未启动）
            if not self.manager.is_running:
                if not self.manager.start():
                    self.logger.error("无法启动串口管理器")
                    return False

            # 注册客户端
            success = self.manager.register_client(
                client_id=self.client_id,
                message_callback=self._on_message_received,
                error_callback=self._on_error
            )

            if not success:
                self.logger.error("无法注册到串口管理器")
                return False

            self.manager_registered = True
            self.running = True

            self.logger.info("关机守护进程已启动，通过串口管理器监听关机请求...")

            # 保持运行状态
            while self.running:
                time.sleep(1.0)

            return True

        except Exception as e:
            self.logger.error(f"启动守护进程失败: {e}")
            return False
        finally:
            self.stop()

    def stop(self):
        """停止守护进程"""
        if self.running:
            self.logger.info("停止关机守护进程...")
            self.running = False

            if self.manager_registered and self.manager:
                self.manager.unregister_client(self.client_id)
                self.manager_registered = False
    
    def _on_message_received(self, message):
        """消息接收回调函数"""
        if not isinstance(message, MessageFrame):
            return

        self.logger.info(f"收到消息: 命令=0x{message.command:02X}, 数据={message.data.hex(' ').upper()}")

        # 处理关机请求
        if message.command == MessageType.SHUTDOWN_REQ or message.command == CommandType.SHUTDOWN_REQUEST:
            self.logger.info("收到电源板关机请求!")
            self._handle_shutdown_request()

    def _on_error(self, error_msg: str):
        """错误回调函数"""
        self.logger.error(f"串口通信错误: {error_msg}")

    def _handle_shutdown_request(self):
        """处理关机请求"""
        if self.shutdown_requested:
            self.logger.warning("关机流程已在进行中，忽略重复请求")
            return

        self.shutdown_requested = True
        self.logger.info("开始执行关机流程...")

        try:
            # 创建关机通知脚本
            self._create_shutdown_notify_script()

            # 等待一小段时间确保脚本创建完成
            time.sleep(0.5)

            # 执行系统关机命令
            self.logger.info("执行系统关机命令...")
            subprocess.run(['sudo', 'shutdown', '-h', 'now'], check=False)

        except Exception as e:
            self.logger.error(f"执行关机流程失败: {e}")

    def _create_shutdown_notify_script(self):
        """创建关机通知脚本"""
        script_content = f'''#!/bin/bash
# N100关机成功通知脚本
# 在系统关机的最后阶段发送关机成功消息给电源板

# 日志函数
log_msg() {{
    echo "$(date): $1" >> /var/log/n100_shutdown.log
}}

log_msg "关机通知脚本开始执行..."

# 等待文件系统同步
sync
log_msg "文件系统同步完成"

# 发送关机成功消息
python3 {os.path.dirname(os.path.abspath(__file__))}/shutdown_notify.py

log_msg "关机成功消息已发送"
'''

        # 写入关机脚本
        script_path = '/etc/init.d/n100_shutdown_notify'
        try:
            with open(script_path, 'w') as f:
                f.write(script_content)

            # 设置执行权限
            os.chmod(script_path, 0o755)

            # 添加到关机序列
            subprocess.run(['sudo', 'update-rc.d', 'n100_shutdown_notify', 'start', '01', '0', '6', '.'],
                          check=False)

            self.logger.info(f"关机通知脚本已创建: {script_path}")

        except Exception as e:
            self.logger.error(f"创建关机通知脚本失败: {e}")
    
    @staticmethod
    def send_shutdown_success(port: str = '/dev/ttyS4'):
        """发送关机成功消息（静态方法，供关机脚本调用）"""
        try:
            # 尝试使用串口管理器
            try:
                manager = get_serial_manager()
                if manager.is_running:
                    client_id = f"shutdown_notify_{int(time.time())}"
                    if manager.register_client(client_id):
                        success = manager.send_message(
                            client_id=client_id,
                            command=CommandType.SHUTDOWN_SUCCESS,
                            data=b'',
                            wait_ack=False,  # 关机时不等待ACK
                            max_retries=1
                        )
                        manager.unregister_client(client_id)
                        if success:
                            print("通过串口管理器发送关机成功消息")
                            return True
            except:
                pass

            # 回退到直接串口通信
            controller = N100PowerController(port=port, timeout=0.5, max_retries=1, use_manager=False)

            if controller.connect():
                success = controller.send_shutdown_success()
                controller.disconnect()

                if success:
                    print("直接发送关机成功消息")
                    return True
                else:
                    print("发送关机成功消息失败")
                    return False
            else:
                print(f"无法连接到串口 {port}")
                return False

        except Exception as e:
            print(f"发送关机成功消息时发生异常: {e}")
            return False


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='N100关机守护进程')
    parser.add_argument('--port', type=str, default='/dev/ttyS4',
                       help='串口设备路径 (默认: /dev/ttyS4)')
    parser.add_argument('--log-file', type=str, default='/var/log/n100_shutdown.log',
                       help='日志文件路径 (默认: /var/log/n100_shutdown.log)')
    parser.add_argument('--daemon', action='store_true',
                       help='以守护进程模式运行')
    parser.add_argument('--send-shutdown-success', action='store_true',
                       help='仅发送关机成功消息（供关机脚本调用）')

    args = parser.parse_args()

    # 如果只是发送关机成功消息
    if args.send_shutdown_success:
        success = N100ShutdownDaemon.send_shutdown_success(args.port)
        sys.exit(0 if success else 1)

    # 创建并启动守护进程
    daemon = N100ShutdownDaemon(port=args.port, log_file=args.log_file)

    try:
        success = daemon.start()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n收到中断信号，正在退出...")
        daemon.stop()
        sys.exit(0)
    except Exception as e:
        print(f"守护进程异常退出: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
