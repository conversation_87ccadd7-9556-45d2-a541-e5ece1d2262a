#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
串口代理架构测试
测试新的串口代理架构是否解决了串口冲突问题
"""

import os
import sys
import time
import subprocess
import threading

# 添加当前目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'src'))

try:
    from memory_serial_simulator import MemorySerial, PowerBoardSimulator
    from serial_proxy_daemon import SerialProxyDaemon
    from serial_proxy_client import SerialProxyClient
    from n100_power_ctrl import N100PowerController, LEDMode
except ImportError as e:
    print(f"错误: 无法导入必要模块: {e}")
    sys.exit(1)


class ProxyArchitectureTester:
    """代理架构测试器"""
    
    def __init__(self):
        # 创建内存串口对
        self.n100_serial = MemorySerial("N100")
        self.power_serial = MemorySerial("PowerBoard")
        self.n100_serial.connect_peer(self.power_serial)
        
        # 创建电源板模拟器
        self.power_simulator = PowerBoardSimulator(self.power_serial)
        
        # 创建串口代理守护进程
        self.proxy_daemon = None
        
        # 测试客户端
        self.clients = []
    
    def setup_proxy_daemon(self):
        """设置串口代理守护进程"""
        print("=== 设置串口代理守护进程 ===")
        
        try:
            # 创建代理守护进程
            self.proxy_daemon = SerialProxyDaemon(
                serial_port="memory_serial",  # 使用内存串口
                socket_path="/tmp/test_n100_proxy.sock",
                log_file="/tmp/test_proxy.log"
            )
            
            # 替换串口对象
            self.proxy_daemon.serial = self.n100_serial
            
            # 启动代理服务
            if self.proxy_daemon.start():
                print("✅ 串口代理守护进程启动成功")
                time.sleep(1)  # 等待服务完全启动
                return True
            else:
                print("❌ 串口代理守护进程启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 设置代理守护进程异常: {e}")
            return False
    
    def test_single_client(self):
        """测试单个客户端"""
        print("\n=== 测试单个客户端 ===")
        
        try:
            # 创建客户端
            client = SerialProxyClient("test_client_1", "/tmp/test_n100_proxy.sock")
            
            # 设置回调
            received_messages = []
            
            def on_message(message):
                received_messages.append(message)
                print(f"客户端1收到: 命令=0x{message.command:02X}")
            
            client.set_message_callback(on_message)
            
            # 连接
            if client.connect():
                print("✅ 客户端1连接成功")
                
                # 发送测试数据
                test_data = bytes([0xAA, 0x02, 0x01, 0x00, 0xFF, 0x55])
                if client.send_data(test_data):
                    print(f"✅ 客户端1发送成功: {test_data.hex(' ').upper()}")
                    
                    # 等待响应
                    time.sleep(1)
                    
                    if received_messages:
                        print(f"✅ 客户端1收到 {len(received_messages)} 条消息")
                        return True
                    else:
                        print("❌ 客户端1未收到响应")
                        return False
                else:
                    print("❌ 客户端1发送失败")
                    return False
            else:
                print("❌ 客户端1连接失败")
                return False
                
        except Exception as e:
            print(f"❌ 单客户端测试异常: {e}")
            return False
        finally:
            if 'client' in locals():
                client.disconnect()
    
    def test_multiple_clients(self):
        """测试多个客户端"""
        print("\n=== 测试多个客户端 ===")
        
        clients = []
        results = []
        
        try:
            # 创建多个客户端
            for i in range(3):
                client = SerialProxyClient(f"test_client_{i+2}", "/tmp/test_n100_proxy.sock")
                
                # 设置回调
                client_messages = []
                
                def make_callback(client_id, msg_list):
                    def on_message(message):
                        msg_list.append(message)
                        print(f"客户端{client_id}收到: 命令=0x{message.command:02X}")
                    return on_message
                
                client.set_message_callback(make_callback(i+2, client_messages))
                
                # 连接
                if client.connect():
                    print(f"✅ 客户端{i+2}连接成功")
                    clients.append((client, client_messages))
                else:
                    print(f"❌ 客户端{i+2}连接失败")
                    return False
            
            # 等待所有客户端连接完成
            time.sleep(1)
            
            # 每个客户端发送不同的命令
            commands = [
                bytes([0xAA, 0x02, 0x01, 0x00, 0xFF, 0x55]),  # LED正常
                bytes([0xAA, 0x02, 0x01, 0x01, 0xFE, 0x55]),  # LED呼吸
                bytes([0xAA, 0x02, 0x02, 0x03, 0xFB, 0x55]),  # 3秒周期
            ]
            
            for i, (client, messages) in enumerate(clients):
                if client.send_data(commands[i]):
                    print(f"✅ 客户端{i+2}发送成功")
                else:
                    print(f"❌ 客户端{i+2}发送失败")
                    return False
            
            # 等待响应
            time.sleep(2)
            
            # 检查所有客户端是否都收到了消息
            all_received = True
            for i, (client, messages) in enumerate(clients):
                if messages:
                    print(f"✅ 客户端{i+2}收到 {len(messages)} 条消息")
                else:
                    print(f"❌ 客户端{i+2}未收到消息")
                    all_received = False
            
            return all_received
            
        except Exception as e:
            print(f"❌ 多客户端测试异常: {e}")
            return False
        finally:
            # 断开所有客户端
            for client, _ in clients:
                client.disconnect()
    
    def test_power_controller_with_proxy(self):
        """测试电源控制器使用代理"""
        print("\n=== 测试电源控制器使用代理 ===")
        
        try:
            # 创建电源控制器（使用代理模式）
            controller = N100PowerController(use_proxy=True)
            
            # 替换代理客户端的socket路径
            controller.proxy_client = SerialProxyClient("power_controller", "/tmp/test_n100_proxy.sock")
            
            # 连接
            if controller._connect_with_proxy():
                print("✅ 电源控制器代理连接成功")
                
                # 测试LED控制
                print("测试LED正常模式...")
                if controller.set_led_mode(LEDMode.NORMAL):
                    print("✅ LED正常模式设置成功")
                    
                    time.sleep(1)
                    
                    print("测试LED呼吸模式...")
                    if controller.set_led_mode(LEDMode.BREATH):
                        print("✅ LED呼吸模式设置成功")
                        return True
                    else:
                        print("❌ LED呼吸模式设置失败")
                        return False
                else:
                    print("❌ LED正常模式设置失败")
                    return False
            else:
                print("❌ 电源控制器代理连接失败")
                return False
                
        except Exception as e:
            print(f"❌ 电源控制器代理测试异常: {e}")
            return False
        finally:
            if 'controller' in locals():
                controller.disconnect()
    
    def test_shutdown_simulation(self):
        """测试关机模拟"""
        print("\n=== 测试关机模拟 ===")
        
        try:
            # 创建关机监听客户端
            shutdown_client = SerialProxyClient("shutdown_listener", "/tmp/test_n100_proxy.sock")
            
            # 设置过滤器只接收关机请求
            shutdown_client.set_message_filter([0x13])  # SHUTDOWN_REQ
            
            shutdown_received = []
            
            def on_shutdown_message(message):
                if message.command == 0x13:
                    shutdown_received.append(message)
                    print("收到关机请求，发送ACK...")
                    
                    # 发送ACK
                    ack_data = bytes([0xAA, 0x01, 0x80, 0x80, 0x55])
                    shutdown_client.send_data(ack_data)
            
            shutdown_client.set_message_callback(on_shutdown_message)
            
            # 连接
            if shutdown_client.connect():
                print("✅ 关机监听客户端连接成功")
                
                # 模拟电源板发送关机请求
                print("模拟电源板发送关机请求...")
                self.power_simulator.send_shutdown_request()
                
                # 等待处理
                time.sleep(2)
                
                if shutdown_received:
                    print("✅ 关机请求处理成功")
                    return True
                else:
                    print("❌ 未收到关机请求")
                    return False
            else:
                print("❌ 关机监听客户端连接失败")
                return False
                
        except Exception as e:
            print(f"❌ 关机模拟测试异常: {e}")
            return False
        finally:
            if 'shutdown_client' in locals():
                shutdown_client.disconnect()
    
    def cleanup(self):
        """清理资源"""
        print("\n=== 清理资源 ===")
        
        # 停止代理守护进程
        if self.proxy_daemon:
            self.proxy_daemon.stop()
        
        # 停止电源板模拟器
        if self.power_simulator:
            self.power_simulator.stop()
        
        # 删除测试文件
        try:
            if os.path.exists("/tmp/test_n100_proxy.sock"):
                os.unlink("/tmp/test_n100_proxy.sock")
            if os.path.exists("/tmp/test_proxy.log"):
                os.unlink("/tmp/test_proxy.log")
        except:
            pass
        
        print("✅ 资源清理完成")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("串口代理架构测试")
        print("=" * 40)
        
        results = {}
        
        try:
            # 启动电源板模拟器
            self.power_simulator.start()
            time.sleep(0.5)
            
            # 设置代理守护进程
            if not self.setup_proxy_daemon():
                print("❌ 代理守护进程设置失败，无法继续测试")
                return False
            
            # 1. 测试单个客户端
            results['single_client'] = self.test_single_client()
            
            # 2. 测试多个客户端
            results['multiple_clients'] = self.test_multiple_clients()
            
            # 3. 测试电源控制器
            results['power_controller'] = self.test_power_controller_with_proxy()
            
            # 4. 测试关机模拟
            results['shutdown_simulation'] = self.test_shutdown_simulation()
            
            # 显示结果
            print(f"\n=== 测试结果总结 ===")
            for test_name, result in results.items():
                status = "✅ 通过" if result else "❌ 失败"
                print(f"{test_name}: {status}")
            
            # 显示统计信息
            print(f"\n=== 通信统计 ===")
            print(f"电源板接收命令: {self.power_simulator.stats['received_commands']}")
            print(f"电源板发送ACK: {self.power_simulator.stats['sent_acks']}")
            print(f"N100发送字节: {self.n100_serial.bytes_sent}")
            print(f"N100接收字节: {self.n100_serial.bytes_received}")
            
            all_passed = all(results.values())
            if all_passed:
                print("\n🎉 所有代理架构测试通过！")
                print("新架构成功解决了串口冲突问题")
            else:
                print("\n❌ 部分代理架构测试失败")
            
            return all_passed
            
        except Exception as e:
            print(f"\n❌ 测试异常: {e}")
            return False
        finally:
            self.cleanup()


def main():
    """主函数"""
    tester = ProxyArchitectureTester()
    
    try:
        success = tester.run_all_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n测试被中断")
        tester.cleanup()
        return 1


if __name__ == "__main__":
    sys.exit(main())
