#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CLI工具测试脚本
使用内存串口模拟器测试power_ctrl_cli.py
"""

import os
import sys
import time
import threading
import subprocess
from typing import Optional

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from memory_serial_simulator import MemorySerial, PowerBoardSimulator
    from n100_power_ctrl import N100PowerController
except ImportError as e:
    print(f"错误: 无法导入必要模块: {e}")
    sys.exit(1)


class CLITester:
    """CLI工具测试器"""
    
    def __init__(self):
        # 创建内存串口对
        self.n100_serial = MemorySerial("N100")
        self.power_serial = MemorySerial("PowerBoard")
        
        # 连接串口对
        self.n100_serial.connect_peer(self.power_serial)
        
        # 创建电源板模拟器
        self.power_simulator = PowerBoardSimulator(self.power_serial)
        
        # 启动模拟器
        self.power_simulator.start()
        time.sleep(0.5)
    
    def test_cli_commands(self):
        """测试CLI命令"""
        print("=== 测试CLI命令 ===")
        
        # 创建控制器并替换串口
        controller = N100PowerController(use_manager=False, max_retries=3, timeout=2.0)
        controller.serial = self.n100_serial
        controller.is_connected = True
        
        # 模拟CLI命令
        commands = [
            ("led normal", "set_led_mode", [0]),
            ("led breath", "set_led_mode", [1]),
            ("breath 1", "set_breath_period", [1]),
            ("breath 3", "set_breath_period", [3]),
            ("breath 5", "set_breath_period", [5]),
            ("shutdown", "send_shutdown_success", [])
        ]
        
        results = {}
        
        for cmd_name, method_name, args in commands:
            print(f"\n测试命令: {cmd_name}")
            
            try:
                method = getattr(controller, method_name)
                if args:
                    success = method(args[0])
                else:
                    success = method()
                
                results[cmd_name] = success
                print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")
                
                time.sleep(0.5)
                
            except Exception as e:
                print(f"   ❌ 异常: {e}")
                results[cmd_name] = False
        
        return results
    
    def test_test_command(self):
        """测试test命令"""
        print("\n=== 测试test命令 ===")
        
        try:
            # 创建控制器
            controller = N100PowerController(use_manager=False, max_retries=3, timeout=2.0)
            controller.serial = self.n100_serial
            controller.is_connected = True
            
            # 执行测试序列
            print("执行测试序列...")
            
            # 1. LED正常模式
            success1 = controller.set_led_mode(0)
            print(f"1. LED正常模式: {'✅' if success1 else '❌'}")
            time.sleep(0.5)
            
            # 2. LED呼吸模式
            success2 = controller.set_led_mode(1)
            print(f"2. LED呼吸模式: {'✅' if success2 else '❌'}")
            time.sleep(0.5)
            
            # 3. 呼吸周期3秒
            success3 = controller.set_breath_period(3)
            print(f"3. 呼吸周期3秒: {'✅' if success3 else '❌'}")
            time.sleep(0.5)
            
            # 4. 关机成功
            success4 = controller.send_shutdown_success()
            print(f"4. 关机成功: {'✅' if success4 else '❌'}")
            
            all_success = all([success1, success2, success3, success4])
            print(f"\ntest命令结果: {'✅ 全部成功' if all_success else '❌ 部分失败'}")
            
            return all_success
            
        except Exception as e:
            print(f"❌ test命令异常: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        if self.power_simulator:
            self.power_simulator.stop()
    
    def run_all_tests(self):
        """运行所有测试"""
        print("CLI工具功能测试")
        print("=" * 30)
        
        try:
            # 1. 测试CLI命令
            cli_results = self.test_cli_commands()
            
            # 2. 测试test命令
            test_result = self.test_test_command()
            
            # 显示结果
            print(f"\n=== 测试结果总结 ===")
            for cmd, result in cli_results.items():
                status = "✅ 通过" if result else "❌ 失败"
                print(f"{cmd}: {status}")
            
            print(f"test命令: {'✅ 通过' if test_result else '❌ 失败'}")
            
            # 显示统计信息
            print(f"\n=== 通信统计 ===")
            print(f"电源板接收命令: {self.power_simulator.stats['received_commands']}")
            print(f"电源板发送ACK: {self.power_simulator.stats['sent_acks']}")
            print(f"N100发送字节: {self.n100_serial.bytes_sent}")
            print(f"N100接收字节: {self.n100_serial.bytes_received}")
            
            all_passed = all(cli_results.values()) and test_result
            if all_passed:
                print("\n🎉 所有CLI测试通过！")
            else:
                print("\n❌ 部分CLI测试失败")
            
            return all_passed
            
        except Exception as e:
            print(f"\n❌ 测试异常: {e}")
            return False
        finally:
            self.cleanup()


def main():
    """主函数"""
    tester = CLITester()
    
    try:
        success = tester.run_all_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n测试被中断")
        tester.cleanup()
        return 1


if __name__ == "__main__":
    sys.exit(main())
