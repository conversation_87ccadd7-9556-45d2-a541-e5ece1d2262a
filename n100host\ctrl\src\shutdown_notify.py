#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100关机通知脚本 - 优化版本
在系统关机时发送关机成功消息给电源板

优化功能：
1. 优先使用串口管理器发送消息
2. 回退到直接串口通信
3. 支持多种发送方式，确保消息能够成功发送
4. 改进的错误处理和日志记录
"""

import os
import sys
import time
import logging

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from serial_manager import get_serial_manager, MessageType
    from protocol import CommandType, create_shutdown_success_frame
    from n100_power_ctrl import N100PowerController
except ImportError:
    # 如果无法导入，使用直接串口通信
    get_serial_manager = None
    MessageType = None
    CommandType = None
    N100PowerController = None


def send_shutdown_success_with_manager(port='/dev/ttyS4'):
    """
    通过串口管理器发送关机成功消息

    参数:
        port (str): 串口设备路径

    返回:
        bool: 发送是否成功
    """
    try:
        if get_serial_manager is None:
            return False

        # 获取串口管理器
        manager = get_serial_manager()

        if not manager.is_running:
            return False

        # 注册临时客户端
        client_id = f"shutdown_notify_{int(time.time())}"
        if not manager.register_client(client_id):
            return False

        try:
            # 发送关机成功消息
            command = CommandType.SHUTDOWN_SUCCESS if CommandType else 0x03
            success = manager.send_message(
                client_id=client_id,
                command=command,
                data=b'',
                wait_ack=False,  # 关机时不等待ACK
                max_retries=1
            )

            return success

        finally:
            # 注销客户端
            manager.unregister_client(client_id)

    except Exception as e:
        # 记录错误但不抛出异常
        try:
            with open('/var/log/n100_shutdown.log', 'a') as f:
                f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} - 串口管理器发送失败: {e}\n")
        except:
            pass
        return False


def send_shutdown_success_direct(port='/dev/ttyS4', baudrate=115200):
    """
    直接通过串口发送关机成功消息
    不依赖n100_power_ctrl模块，确保在系统关机时也能工作
    """
    try:
        # 动态导入serial模块
        import serial

        # 关机成功消息帧: AA 01 03 FD 55
        shutdown_frame = bytes([0xAA, 0x01, 0x03, 0xFD, 0x55])

        # 打开串口
        ser = serial.Serial(port, baudrate, timeout=1)
        
        # 发送关机成功消息
        ser.write(shutdown_frame)
        ser.flush()
        
        # 等待一小段时间确保发送完成
        time.sleep(0.1)
        
        # 关闭串口
        ser.close()
        
        return True
        
    except Exception as e:
        # 记录错误但不抛出异常，避免影响关机流程
        try:
            with open('/var/log/n100_shutdown.log', 'a') as f:
                f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} - 发送关机成功消息失败: {e}\n")
        except:
            pass
        return False


def send_shutdown_success_with_controller(port='/dev/ttyS4'):
    """
    使用N100PowerController发送关机成功消息
    """
    try:
        if N100PowerController is None:
            return False

        controller = N100PowerController(port=port, timeout=0.5, max_retries=1)
        
        if controller.connect():
            success = controller.send_shutdown_success()
            controller.disconnect()
            return success
        else:
            return False
            
    except Exception:
        return False


def main():
    """主函数"""
    port = '/dev/ttyS4'
    
    # 记录开始执行
    try:
        with open('/var/log/n100_shutdown.log', 'a') as f:
            f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} - 关机通知脚本开始执行\n")
    except:
        pass
    
    # 等待文件系统同步
    try:
        os.sync()
    except:
        pass

    # 尝试多种发送方式，确保消息能够发送成功
    success = False

    # 1. 优先使用串口管理器
    try:
        success = send_shutdown_success_with_manager(port)
        if success:
            method = "串口管理器"
    except:
        pass

    # 2. 如果串口管理器失败，使用控制器
    if not success:
        try:
            success = send_shutdown_success_with_controller(port)
            if success:
                method = "电源控制器"
        except:
            pass

    # 3. 如果控制器方式失败，使用直接方式
    if not success:
        success = send_shutdown_success_direct(port)
        if success:
            method = "直接串口"
    
    # 记录结果
    try:
        with open('/var/log/n100_shutdown.log', 'a') as f:
            if success:
                f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} - 关机成功消息发送成功（方式: {method}）\n")
            else:
                f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} - 关机成功消息发送失败（所有方式都失败）\n")
    except:
        pass
    
    # 返回状态码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
