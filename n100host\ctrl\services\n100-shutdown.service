[Unit]
Description=N100 Shutdown Daemon
Documentation=N100 power board shutdown request monitor
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
ExecStart=/usr/bin/python3 /opt/n100/ctrl/n100_shutdown_daemon.py --port /dev/ttyS4
ExecStop=/bin/kill -TERM $MAINPID
# 移除自动重启，避免持续占用串口
# Restart=always
# RestartSec=5
StandardOutput=journal
StandardError=journal

# 环境变量
Environment=PYTHONPATH=/opt/n100/ctrl

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log /tmp

[Install]
# 改为手动启动，不自动启动
# WantedBy=multi-user.target
