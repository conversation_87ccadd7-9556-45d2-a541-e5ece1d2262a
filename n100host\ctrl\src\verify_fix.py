#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证修复脚本
验证ACK等待机制的修复是否有效
"""

import os
import sys
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from memory_serial_simulator import MemorySerial, PowerBoardSimulator, CommunicationTester
    from n100_power_ctrl import N100PowerController, LEDMode
    from serial_manager import SerialManager, MessageType
except ImportError as e:
    print(f"错误: 无法导入必要模块: {e}")
    sys.exit(1)


def test_ack_fix():
    """测试ACK修复"""
    print("=== 验证ACK等待机制修复 ===")
    
    # 创建内存串口对
    n100_serial = MemorySerial("N100")
    power_serial = MemorySerial("PowerBoard")
    n100_serial.connect_peer(power_serial)
    
    # 创建电源板模拟器
    power_simulator = PowerBoardSimulator(power_serial)
    power_simulator.start()
    time.sleep(0.5)
    
    try:
        # 测试串口管理器
        print("\n1. 测试串口管理器ACK等待...")
        
        # 创建串口管理器
        manager = SerialManager()
        manager.serial = n100_serial
        manager.is_running = True
        
        # 注册客户端
        client_id = "test_client"
        success = manager.register_client(client_id)
        print(f"   客户端注册: {'✅ 成功' if success else '❌ 失败'}")
        
        if success:
            # 发送消息
            print("   发送LED命令...")
            success = manager.send_message(
                client_id=client_id,
                command=0x01,  # LED命令
                data=bytes([0x00]),  # 正常模式
                max_retries=3
            )
            print(f"   消息发送: {'✅ 成功' if success else '❌ 失败'}")
            
            manager.unregister_client(client_id)
        
        # 测试电源控制器
        print("\n2. 测试电源控制器...")
        
        controller = N100PowerController(use_manager=False, max_retries=3, timeout=2.0)
        controller.serial = n100_serial
        controller.is_connected = True
        
        print("   发送LED呼吸模式命令...")
        success = controller.set_led_mode(LEDMode.BREATH)
        print(f"   命令执行: {'✅ 成功' if success else '❌ 失败'}")
        
        # 显示统计信息
        print(f"\n=== 通信统计 ===")
        print(f"电源板接收命令: {power_simulator.stats['received_commands']}")
        print(f"电源板发送ACK: {power_simulator.stats['sent_acks']}")
        print(f"N100发送字节: {n100_serial.bytes_sent}")
        print(f"N100接收字节: {n100_serial.bytes_received}")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False
    finally:
        power_simulator.stop()


def test_cli_options():
    """测试CLI选项"""
    print("\n=== 验证CLI选项 ===")
    
    try:
        import argparse
        
        # 模拟CLI参数解析
        parser = argparse.ArgumentParser()
        parser.add_argument('--no-manager', action='store_true')
        
        # 测试不同参数
        args1 = parser.parse_args(['--no-manager'])
        args2 = parser.parse_args([])
        
        print(f"--no-manager 参数: {args1.no_manager}")
        print(f"默认参数: {args2.no_manager}")
        
        # 测试控制器创建
        controller1 = N100PowerController(use_manager=not args1.no_manager)
        controller2 = N100PowerController(use_manager=not args2.no_manager)
        
        print(f"--no-manager 模式使用管理器: {controller1.use_manager}")
        print(f"默认模式使用管理器: {controller2.use_manager}")
        
        return True
        
    except Exception as e:
        print(f"❌ CLI选项测试异常: {e}")
        return False


def main():
    """主函数"""
    print("修复验证测试")
    print("=" * 30)
    
    # 1. 测试ACK修复
    ack_ok = test_ack_fix()
    
    # 2. 测试CLI选项
    cli_ok = test_cli_options()
    
    # 显示结果
    print(f"\n=== 验证结果 ===")
    print(f"ACK等待机制: {'✅ 修复成功' if ack_ok else '❌ 仍有问题'}")
    print(f"CLI选项功能: {'✅ 正常' if cli_ok else '❌ 异常'}")
    
    if ack_ok and cli_ok:
        print("\n🎉 所有修复验证通过！")
        print("\n使用建议:")
        print("1. 如果串口管理器有问题，使用: --no-manager")
        print("2. 如果电源板不响应，检查硬件连接")
        print("3. 如果权限不足，运行: sudo chmod 666 /dev/ttyS4")
    else:
        print("\n❌ 部分修复验证失败，需要进一步调试")
    
    return 0 if (ack_ok and cli_ok) else 1


if __name__ == "__main__":
    sys.exit(main())
