[Unit]
Description=N100 Serial Communication Manager
Documentation=N100 serial port communication manager for ttyS4
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
ExecStart=/usr/bin/python3 /opt/n100/ctrl/serial_manager_daemon.py --port /dev/ttyS4
ExecStop=/bin/kill -TERM $MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

# 环境变量
Environment=PYTHONPATH=/opt/n100/ctrl

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log /tmp

[Install]
WantedBy=multi-user.target
