#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试简化后的N100电源控制系统
验证核心功能是否正常工作
"""

import os
import sys
from unittest.mock import MagicMock, patch

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'src'))


def test_core_module_import():
    """测试核心模块导入"""
    print("=== 测试核心模块导入 ===")
    
    # 模拟serial模块
    mock_serial = MagicMock()
    mock_serial.Serial = MagicMock()
    mock_serial.EIGHTBITS = 8
    mock_serial.PARITY_NONE = 'N'
    mock_serial.STOPBITS_ONE = 1
    
    with patch.dict('sys.modules', {'serial': mock_serial}):
        try:
            from n100_power_ctrl import N100PowerController, LEDMode, BreathPeriod, PowerCommand
            print("✓ n100_power_ctrl模块导入成功")
            
            # 验证枚举
            assert LEDMode.NORMAL == 0
            assert LEDMode.BREATH == 1
            print("✓ LEDMode枚举正确")
            
            assert BreathPeriod.PERIOD_1S == 1
            assert BreathPeriod.PERIOD_3S == 3
            assert BreathPeriod.PERIOD_5S == 5
            print("✓ BreathPeriod枚举正确")
            
            return True
            
        except ImportError as e:
            print(f"✗ 核心模块导入失败: {e}")
            return False


def test_cli_module_import():
    """测试CLI模块导入"""
    print("\n=== 测试CLI模块导入 ===")
    
    # 模拟serial模块
    mock_serial = MagicMock()
    mock_serial.Serial = MagicMock()
    mock_serial.EIGHTBITS = 8
    mock_serial.PARITY_NONE = 'N'
    mock_serial.STOPBITS_ONE = 1
    
    with patch.dict('sys.modules', {'serial': mock_serial}):
        try:
            import power_ctrl_cli
            print("✓ power_ctrl_cli模块导入成功")
            
            # 检查main函数
            if hasattr(power_ctrl_cli, 'main'):
                print("✓ main函数存在")
            else:
                print("✗ main函数不存在")
                return False
            
            return True
            
        except ImportError as e:
            print(f"✗ CLI模块导入失败: {e}")
            return False


def test_controller_basic_functionality():
    """测试控制器基本功能"""
    print("\n=== 测试控制器基本功能 ===")
    
    # 模拟serial模块
    mock_serial = MagicMock()
    mock_serial_instance = MagicMock()
    mock_serial.Serial.return_value = mock_serial_instance
    mock_serial.EIGHTBITS = 8
    mock_serial.PARITY_NONE = 'N'
    mock_serial.STOPBITS_ONE = 1
    
    with patch.dict('sys.modules', {'serial': mock_serial}):
        try:
            from n100_power_ctrl import N100PowerController
            
            # 创建控制器实例
            controller = N100PowerController()
            
            # 验证默认参数
            if controller.max_retries == 10:
                print("✓ 默认重试次数正确 (10次)")
            else:
                print(f"✗ 默认重试次数错误: {controller.max_retries}")
                return False
            
            # 验证端口配置
            if controller.port == '/dev/ttyS4':
                print("✓ 默认串口配置正确")
            else:
                print(f"✗ 默认串口配置错误: {controller.port}")
                return False
            
            # 验证波特率
            if controller.baudrate == 115200:
                print("✓ 默认波特率配置正确")
            else:
                print(f"✗ 默认波特率配置错误: {controller.baudrate}")
                return False
            
            return True
            
        except Exception as e:
            print(f"✗ 控制器测试失败: {e}")
            return False


def test_file_structure():
    """测试文件结构"""
    print("\n=== 测试文件结构 ===")
    
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    # 必需的文件
    required_files = [
        'src/n100_power_ctrl.py',
        'src/power_ctrl_cli.py',
        'src/n100_shutdown_daemon.py',
        'src/shutdown_notify.py',
        'scripts/install_shutdown_system.sh',
        'scripts/n100-shutdown-hook.sh',
        'services/n100-shutdown.service',
        'tests/test_syntax.py',
        'tests/test_shutdown_system.py',
        'examples/example.py',
        'docs/README.md',
        'docs/USAGE.md',
        'README.md',
        'README_SHUTDOWN.md',
        'Makefile',
        'requirements.txt',
        'setup.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = os.path.join(base_dir, file_path)
        if not os.path.exists(full_path):
            missing_files.append(file_path)
    
    if not missing_files:
        print("✓ 所有必需文件都存在")
        return True
    else:
        print(f"✗ 缺失文件: {missing_files}")
        return False


def test_removed_files():
    """测试已删除的文件"""
    print("\n=== 测试已删除的文件 ===")
    
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    # 应该被删除的文件
    removed_files = [
        'QUICK_FIX.md',
        'TROUBLESHOOTING.md',
        'UBUNTU_USAGE.md',
        'RETRY_COUNT_UPDATE.md',
        'PROJECT_STRUCTURE.md',
        'src/serial_manager.py',
        'tests/test_power_ctrl_cli_fix.py',
        'tests/test_retry_count.py',
        'tests/test_serial_management.py',
        'scripts/diagnose.sh',
        'scripts/fix_smart_service.sh',
        'scripts/smart_shutdown_start.sh',
        'scripts/smart_shutdown_stop.sh',
        'services/n100-shutdown-smart.service',
        'docs/SERIAL_MANAGEMENT.md',
        'docs/SHUTDOWN_SYSTEM.md'
    ]
    
    existing_files = []
    for file_path in removed_files:
        full_path = os.path.join(base_dir, file_path)
        if os.path.exists(full_path):
            existing_files.append(file_path)
    
    if not existing_files:
        print("✓ 所有冗余文件已成功删除")
        return True
    else:
        print(f"✗ 以下文件仍然存在: {existing_files}")
        return False


def test_makefile_commands():
    """测试Makefile命令"""
    print("\n=== 测试Makefile命令 ===")
    
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    makefile_path = os.path.join(base_dir, 'Makefile')
    
    try:
        with open(makefile_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查基本命令是否存在
        required_commands = [
            'led-normal:',
            'led-breath:',
            'breath-3s:',
            'test:',
            'example:',
            'install-shutdown:',
            'clean:'
        ]
        
        missing_commands = []
        for cmd in required_commands:
            if cmd not in content:
                missing_commands.append(cmd)
        
        # 检查是否删除了不必要的命令
        removed_commands = [
            'check-serial:',
            'release-serial:',
            'test-serial:',
            'check:',
            'force-release:',
            'test-cli-fix:',
            'test-retry:'
        ]
        
        existing_commands = []
        for cmd in removed_commands:
            if cmd in content:
                existing_commands.append(cmd)
        
        if not missing_commands and not existing_commands:
            print("✓ Makefile命令配置正确")
            return True
        else:
            if missing_commands:
                print(f"✗ 缺失命令: {missing_commands}")
            if existing_commands:
                print(f"✗ 仍存在的冗余命令: {existing_commands}")
            return False
            
    except Exception as e:
        print(f"✗ 读取Makefile失败: {e}")
        return False


def main():
    """主测试函数"""
    print("N100电源控制系统简化版本测试")
    print("=" * 50)
    
    tests = [
        ("核心模块导入", test_core_module_import),
        ("CLI模块导入", test_cli_module_import),
        ("控制器基本功能", test_controller_basic_functionality),
        ("文件结构", test_file_structure),
        ("已删除文件", test_removed_files),
        ("Makefile命令", test_makefile_commands),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统简化成功。")
        print("\n简化效果:")
        print("- 删除了冗余的文档和测试文件")
        print("- 移除了复杂的串口管理功能")
        print("- 保留了核心的电源控制和关机功能")
        print("- 简化了Makefile命令")
        print("- 整理了目录结构")
        return 0
    else:
        print("❌ 部分测试失败，请检查系统配置。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
