#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100关机守护进程 - 串口代理版本
通过串口代理服务监听关机请求，避免串口冲突

功能:
1. 通过串口代理监听电源板的关机请求
2. 收到关机请求后立即发送ACK应答
3. 执行系统关机命令
4. 在关机过程中发送关机成功消息
"""

import os
import sys
import time
import signal
import logging
import subprocess
import threading
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from serial_proxy_client import SerialProxyClient
    from serial_proxy_daemon import ProxyMessage, MessageType
except ImportError as e:
    print(f"错误: 无法导入串口代理模块: {e}")
    sys.exit(1)


class N100ShutdownDaemonProxy:
    """N100关机守护进程 - 代理版本"""
    
    def __init__(self, 
                 socket_path: str = '/tmp/n100_serial_proxy.sock',
                 log_file: str = '/var/log/n100_shutdown_proxy.log'):
        """
        初始化关机守护进程
        
        参数:
            socket_path: 串口代理Socket路径
            log_file: 日志文件路径
        """
        self.socket_path = socket_path
        self.log_file = log_file
        
        # 代理客户端
        self.proxy_client = None
        self.running = False
        
        # 关机状态
        self.shutdown_in_progress = False
        
        # 日志配置
        self.setup_logging()
        
        # 信号处理
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
    
    def setup_logging(self):
        """设置日志"""
        # 确保日志目录存在
        log_dir = Path(self.log_file).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('N100ShutdownDaemonProxy')
    
    def start(self) -> bool:
        """启动守护进程"""
        try:
            self.logger.info("启动N100关机守护进程（代理版本）...")
            
            # 创建代理客户端
            self.proxy_client = SerialProxyClient("shutdown_daemon")
            
            # 设置回调
            self.proxy_client.set_message_callback(self._on_message)
            self.proxy_client.set_error_callback(self._on_error)
            
            # 设置消息过滤器（只接收关机请求）
            self.proxy_client.set_message_filter([MessageType.SHUTDOWN_REQ])
            
            # 连接到代理服务
            if not self.proxy_client.connect():
                self.logger.error("无法连接到串口代理服务")
                return False
            
            self.running = True
            self.logger.info("关机守护进程启动成功，开始监听关机请求...")
            return True
            
        except Exception as e:
            self.logger.error(f"启动守护进程失败: {e}")
            return False
    
    def stop(self):
        """停止守护进程"""
        self.logger.info("停止关机守护进程...")
        self.running = False
        
        if self.proxy_client:
            self.proxy_client.disconnect()
        
        self.logger.info("关机守护进程已停止")
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，准备停止守护进程...")
        self.stop()
        sys.exit(0)
    
    def _on_message(self, message: ProxyMessage):
        """处理接收到的消息"""
        try:
            if message.msg_type == 'serial_data' and message.command == MessageType.SHUTDOWN_REQ:
                self.logger.info("收到电源板关机请求")
                
                # 立即发送ACK应答
                self._send_ack_response()
                
                # 执行关机流程
                self._execute_shutdown()
        
        except Exception as e:
            self.logger.error(f"处理消息异常: {e}")
    
    def _on_error(self, error_msg: str):
        """处理错误"""
        self.logger.error(f"代理客户端错误: {error_msg}")
    
    def _send_ack_response(self):
        """发送ACK应答"""
        try:
            # 创建ACK帧
            ack_frame = bytes([0xAA, 0x01, 0x80, 0x80, 0x55])
            
            # 通过代理发送
            if self.proxy_client.send_data(ack_frame):
                self.logger.info("已发送ACK应答")
            else:
                self.logger.error("发送ACK应答失败")
        
        except Exception as e:
            self.logger.error(f"发送ACK应答异常: {e}")
    
    def _execute_shutdown(self):
        """执行关机流程"""
        if self.shutdown_in_progress:
            self.logger.warning("关机流程已在进行中，忽略重复请求")
            return
        
        self.shutdown_in_progress = True
        
        try:
            self.logger.info("开始执行关机流程...")
            
            # 启动关机线程
            shutdown_thread = threading.Thread(target=self._shutdown_worker, daemon=True)
            shutdown_thread.start()
        
        except Exception as e:
            self.logger.error(f"执行关机流程异常: {e}")
            self.shutdown_in_progress = False
    
    def _shutdown_worker(self):
        """关机工作线程"""
        try:
            # 等待一小段时间确保ACK发送完成
            time.sleep(0.5)
            
            # 执行关机命令
            self.logger.info("执行关机命令: sudo shutdown -h now")
            
            # 启动关机成功通知线程
            notify_thread = threading.Thread(target=self._shutdown_success_notifier, daemon=True)
            notify_thread.start()
            
            # 执行系统关机
            subprocess.run(['sudo', 'shutdown', '-h', 'now'], check=True)
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"关机命令执行失败: {e}")
        except Exception as e:
            self.logger.error(f"关机工作线程异常: {e}")
    
    def _shutdown_success_notifier(self):
        """关机成功通知线程"""
        try:
            # 等待系统开始关机流程
            time.sleep(2)
            
            # 检测系统关机状态
            max_wait = 30  # 最多等待30秒
            for i in range(max_wait):
                try:
                    # 检查是否有关机进程
                    result = subprocess.run(['pgrep', 'shutdown'], 
                                          capture_output=True, text=True)
                    
                    if result.returncode == 0:
                        self.logger.info("检测到系统关机进程，准备发送关机成功消息")
                        break
                    
                    time.sleep(1)
                
                except Exception:
                    time.sleep(1)
            
            # 等待文件系统同步
            time.sleep(3)
            
            # 发送关机成功消息
            self._send_shutdown_success()
            
        except Exception as e:
            self.logger.error(f"关机成功通知异常: {e}")
    
    def _send_shutdown_success(self):
        """发送关机成功消息"""
        try:
            # 创建关机成功帧
            shutdown_frame = bytes([0xAA, 0x01, 0x03, 0xFD, 0x55])
            
            # 通过代理发送
            if self.proxy_client and self.proxy_client.send_data(shutdown_frame):
                self.logger.info("已发送关机成功消息")
            else:
                self.logger.error("发送关机成功消息失败")
        
        except Exception as e:
            self.logger.error(f"发送关机成功消息异常: {e}")
    
    def run(self):
        """运行守护进程"""
        if not self.start():
            return 1
        
        try:
            # 保持运行
            while self.running:
                time.sleep(1)
        
        except KeyboardInterrupt:
            self.logger.info("收到中断信号")
        
        finally:
            self.stop()
        
        return 0


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='N100关机守护进程（代理版本）')
    parser.add_argument('--socket', default='/tmp/n100_serial_proxy.sock', 
                       help='串口代理Socket路径')
    parser.add_argument('--log', default='/var/log/n100_shutdown_proxy.log', 
                       help='日志文件路径')
    
    args = parser.parse_args()
    
    # 创建并运行守护进程
    daemon = N100ShutdownDaemonProxy(
        socket_path=args.socket,
        log_file=args.log
    )
    
    return daemon.run()


if __name__ == "__main__":
    sys.exit(main())
