# N100电源控制器使用指南

## 快速开始

### 1. 安装依赖
```bash
pip install pyserial
```

### 2. 基本使用示例
```python
from n100_power_ctrl import N100PowerController

# 创建控制器
controller = N100PowerController(port='/dev/ttyS4')

# 连接串口
if controller.connect():
    # 设置LED为呼吸模式
    controller.set_led_breath()
    
    # 设置呼吸周期为3秒
    controller.set_breath_3s()
    
    # 发送关机成功
    controller.send_shutdown_success()
    
    # 断开连接
    controller.disconnect()
```

### 3. 命令行使用
```bash
# 设置LED模式
python power_ctrl_cli.py led normal    # 正常模式
python power_ctrl_cli.py led breath    # 呼吸模式

# 设置呼吸周期
python power_ctrl_cli.py breath 1      # 1秒周期
python power_ctrl_cli.py breath 3      # 3秒周期
python power_ctrl_cli.py breath 5      # 5秒周期

# 发送关机成功
python power_ctrl_cli.py shutdown

# 运行完整测试
python power_ctrl_cli.py test
```

## 支持的消息帧

| 功能 | 消息帧 | 说明 |
|------|--------|------|
| 设置正常模式 | `AA 02 01 00 FF 55` | LED正常模式 |
| 设置呼吸模式 | `AA 02 01 01 FE 55` | LED呼吸模式 |
| 1秒周期 | `AA 02 02 01 FD 55` | 呼吸灯1秒周期 |
| 3秒周期 | `AA 02 02 03 FB 55` | 呼吸灯3秒周期 |
| 5秒周期 | `AA 02 02 05 F9 55` | 呼吸灯5秒周期 |
| 关机成功 | `AA 01 03 FD 55` | 关机成功消息 |
| 通用应答 | `AA 01 80 80 55` | ACK应答帧 |

## API接口

### 主要方法

#### 连接管理
- `connect()` - 连接串口
- `disconnect()` - 断开串口
- `is_port_connected()` - 检查连接状态

#### LED控制
- `set_led_mode(mode)` - 设置LED模式
- `set_led_normal()` - 设置LED正常模式
- `set_led_breath()` - 设置LED呼吸模式

#### 呼吸灯控制
- `set_breath_period(period)` - 设置呼吸周期
- `set_breath_1s()` - 设置1秒呼吸周期
- `set_breath_3s()` - 设置3秒呼吸周期
- `set_breath_5s()` - 设置5秒呼吸周期

#### 系统控制
- `send_shutdown_success()` - 发送关机成功消息
- `send_custom_command(cmd, data)` - 发送自定义命令

#### 回调设置
- `set_ack_callback(callback)` - 设置ACK接收回调
- `set_error_callback(callback)` - 设置错误回调

### 枚举类型

#### LEDMode
- `LEDMode.NORMAL` (0x00) - 正常模式
- `LEDMode.BREATH` (0x01) - 呼吸模式

#### BreathPeriod
- `BreathPeriod.PERIOD_1S` (1) - 1秒周期
- `BreathPeriod.PERIOD_3S` (3) - 3秒周期
- `BreathPeriod.PERIOD_5S` (5) - 5秒周期

#### PowerCommand
- `PowerCommand.LED_MODE` (0x01) - LED模式命令
- `PowerCommand.BREATH_PERIOD` (0x02) - 呼吸周期命令
- `PowerCommand.SHUTDOWN` (0x03) - 关机命令
- `PowerCommand.ACK` (0x80) - 应答命令

## 高级用法

### 回调函数示例
```python
def on_ack_received(command):
    print(f"收到ACK应答，命令: 0x{command:02X}")

def on_error(error_msg):
    print(f"发生错误: {error_msg}")

controller = N100PowerController()
controller.set_ack_callback(on_ack_received)
controller.set_error_callback(on_error)
```

### 自定义命令
```python
# 发送自定义命令
controller.send_custom_command(0x01, b'\x00')  # 等同于设置LED正常模式
```

### 配置参数
```python
controller = N100PowerController(
    port='/dev/ttyS4',      # 串口设备
    baudrate=115200,        # 波特率
    timeout=1.0,           # 超时时间
    max_retries=10         # 最大重试次数
)
```

## 错误处理

程序具有完善的错误处理机制：

1. **连接错误**: 自动检测串口连接状态
2. **超时重试**: 未收到ACK时自动重试
3. **校验验证**: 自动验证接收到的ACK帧
4. **异常捕获**: 捕获并报告所有异常

## 调试信息

程序会输出详细的调试信息：
- 发送的帧数据（16进制）
- 接收的原始数据
- ACK应答确认
- 重试信息
- 错误信息

## 注意事项

1. 确保串口设备 `/dev/ttyS4` 存在且有访问权限
2. 确保电源板已正确连接并处于工作状态
3. 每个命令发送后会等待ACK应答，超时会自动重试
4. 建议在命令间添加适当的延迟以避免冲突

## 文件结构

```
n100host/ctrl/
├── n100_power_ctrl.py    # 核心控制器类
├── power_ctrl_cli.py     # 命令行工具
├── example.py            # 使用示例
├── test_syntax.py        # 语法测试
├── README.md             # 详细说明
└── USAGE.md              # 使用指南
```

## 测试验证

运行语法测试：
```bash
python test_syntax.py
```

运行使用示例：
```bash
python example.py
```

运行命令行测试：
```bash
python power_ctrl_cli.py test
```
