#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
电源板模拟器
模拟电源板的串口通信行为，用于测试N100通信功能

功能：
1. 监听N100发送的命令
2. 自动发送ACK应答帧
3. 模拟发送关机请求
4. 记录通信日志
"""

import os
import sys
import time
import threading
import signal
from typing import Optional

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from protocol import (
        ProtocolFrame, CommandType, FrameConstants,
        validate_frame, create_ack_frame, create_shutdown_request_frame,
        ProtocolParser
    )
    import serial
except ImportError as e:
    print(f"错误: 无法导入必要模块: {e}")
    sys.exit(1)


class PowerBoardSimulator:
    """电源板模拟器"""
    
    def __init__(self, port: str = '/dev/ttyS4', baudrate: int = 115200):
        """
        初始化电源板模拟器
        
        参数:
            port (str): 串口设备路径
            baudrate (int): 波特率
        """
        self.port = port
        self.baudrate = baudrate
        self.serial = None
        self.running = False
        self.parser = ProtocolParser()
        
        # 统计信息
        self.stats = {
            'received_commands': 0,
            'sent_acks': 0,
            'sent_shutdown_requests': 0,
            'errors': 0
        }
        
        # 信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在停止模拟器...")
        self.stop()
    
    def start(self) -> bool:
        """启动模拟器"""
        print("=== 电源板模拟器启动 ===")
        print(f"端口: {self.port}")
        print(f"波特率: {self.baudrate}")
        
        try:
            # 打开串口
            self.serial = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=0.1
            )
            
            self.running = True
            print("✅ 串口打开成功，开始监听...")
            
            # 启动监听线程
            listen_thread = threading.Thread(target=self._listen_loop, daemon=True)
            listen_thread.start()
            
            return True
            
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            return False
    
    def stop(self):
        """停止模拟器"""
        if self.running:
            self.running = False
            
            if self.serial and self.serial.is_open:
                self.serial.close()
            
            print("\n=== 模拟器已停止 ===")
            self._print_stats()
    
    def _listen_loop(self):
        """监听循环"""
        while self.running:
            try:
                if self.serial.in_waiting > 0:
                    data = self.serial.read(self.serial.in_waiting)
                    self._process_received_data(data)
                
                time.sleep(0.01)  # 短暂休眠
                
            except Exception as e:
                print(f"监听异常: {e}")
                self.stats['errors'] += 1
                time.sleep(0.1)
    
    def _process_received_data(self, data: bytes):
        """处理接收到的数据"""
        print(f"[接收] 原始数据: {data.hex(' ').upper()}")
        
        # 解析帧
        frames = self.parser.feed_data(data)
        
        for frame in frames:
            self._handle_frame(frame)
    
    def _handle_frame(self, frame: ProtocolFrame):
        """处理接收到的帧"""
        print(f"[解析] 命令=0x{frame.command:02X}, 数据={frame.data.hex(' ').upper()}")
        
        self.stats['received_commands'] += 1
        
        # 根据命令类型处理
        if frame.command == CommandType.LED_MODE:
            mode = frame.data[0] if frame.data else 0
            mode_name = "呼吸模式" if mode == 1 else "正常模式"
            print(f"[命令] LED模式设置: {mode_name}")
            
        elif frame.command == CommandType.BREATH_PERIOD:
            period = frame.data[0] if frame.data else 0
            print(f"[命令] 呼吸周期设置: {period}秒")
            
        elif frame.command == CommandType.SHUTDOWN_SUCCESS:
            print(f"[命令] 收到关机成功通知")
            
        elif frame.command == CommandType.ACK:
            print(f"[命令] 收到ACK应答")
            return  # ACK不需要回复
            
        else:
            print(f"[命令] 未知命令: 0x{frame.command:02X}")
        
        # 发送ACK应答
        self._send_ack()
    
    def _send_ack(self):
        """发送ACK应答"""
        try:
            ack_frame = create_ack_frame()
            ack_bytes = ack_frame.to_bytes()
            
            self.serial.write(ack_bytes)
            self.serial.flush()
            
            print(f"[发送] ACK应答: {ack_bytes.hex(' ').upper()}")
            self.stats['sent_acks'] += 1
            
        except Exception as e:
            print(f"发送ACK失败: {e}")
            self.stats['errors'] += 1
    
    def send_shutdown_request(self):
        """发送关机请求"""
        try:
            shutdown_frame = create_shutdown_request_frame()
            shutdown_bytes = shutdown_frame.to_bytes()
            
            self.serial.write(shutdown_bytes)
            self.serial.flush()
            
            print(f"[发送] 关机请求: {shutdown_bytes.hex(' ').upper()}")
            self.stats['sent_shutdown_requests'] += 1
            
        except Exception as e:
            print(f"发送关机请求失败: {e}")
            self.stats['errors'] += 1
    
    def _print_stats(self):
        """打印统计信息"""
        print("=== 通信统计 ===")
        print(f"接收命令数: {self.stats['received_commands']}")
        print(f"发送ACK数: {self.stats['sent_acks']}")
        print(f"发送关机请求数: {self.stats['sent_shutdown_requests']}")
        print(f"错误次数: {self.stats['errors']}")
    
    def interactive_mode(self):
        """交互模式"""
        print("\n=== 交互模式 ===")
        print("命令:")
        print("  s - 发送关机请求")
        print("  q - 退出")
        print("  h - 显示帮助")
        
        while self.running:
            try:
                cmd = input("\n请输入命令: ").strip().lower()
                
                if cmd == 'q':
                    break
                elif cmd == 's':
                    self.send_shutdown_request()
                elif cmd == 'h':
                    print("命令:")
                    print("  s - 发送关机请求")
                    print("  q - 退出")
                    print("  h - 显示帮助")
                else:
                    print("无效命令，输入 h 查看帮助")
                    
            except KeyboardInterrupt:
                break
            except EOFError:
                break


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='电源板模拟器')
    parser.add_argument('--port', type=str, default='/dev/ttyS4',
                       help='串口设备路径 (默认: /dev/ttyS4)')
    parser.add_argument('--baudrate', type=int, default=115200,
                       help='波特率 (默认: 115200)')
    parser.add_argument('--auto-shutdown', type=int, default=0,
                       help='自动发送关机请求的延迟秒数 (0=不自动发送)')
    
    args = parser.parse_args()
    
    # 创建模拟器
    simulator = PowerBoardSimulator(port=args.port, baudrate=args.baudrate)
    
    try:
        if simulator.start():
            # 如果设置了自动关机，启动定时器
            if args.auto_shutdown > 0:
                def auto_shutdown():
                    time.sleep(args.auto_shutdown)
                    if simulator.running:
                        print(f"\n[自动] {args.auto_shutdown}秒后发送关机请求")
                        simulator.send_shutdown_request()
                
                timer_thread = threading.Thread(target=auto_shutdown, daemon=True)
                timer_thread.start()
            
            # 进入交互模式
            simulator.interactive_mode()
        
    except Exception as e:
        print(f"模拟器异常: {e}")
    
    finally:
        simulator.stop()


if __name__ == "__main__":
    main()
