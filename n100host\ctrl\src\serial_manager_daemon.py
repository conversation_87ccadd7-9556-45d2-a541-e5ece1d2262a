#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
串口管理器守护进程
作为系统服务运行，统一管理N100与电源板之间的ttyS4串口通信

功能：
1. 启动并维护串口管理器
2. 提供统一的串口访问接口
3. 自动处理通用应答帧
4. 支持多客户端并发访问
5. 日志记录和错误处理
"""

import os
import sys
import time
import signal
import logging
import argparse
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from serial_manager import SerialManager, get_serial_manager, start_serial_manager, stop_serial_manager
except ImportError as e:
    print(f"错误: 无法导入串口管理器模块: {e}")
    sys.exit(1)


class SerialManagerDaemon:
    """串口管理器守护进程类"""
    
    def __init__(self, port: str = '/dev/ttyS4', log_file: str = '/var/log/n100_serial_manager.log'):
        """
        初始化守护进程
        
        参数:
            port (str): 串口设备路径
            log_file (str): 日志文件路径
        """
        self.port = port
        self.log_file = log_file
        self.running = False
        self.manager = None
        
        # 设置日志
        self._setup_logging()
        
        # 注册信号处理器
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
        
        self.logger.info("串口管理器守护进程初始化完成")
    
    def _setup_logging(self):
        """设置日志记录"""
        # 确保日志目录存在
        log_dir = os.path.dirname(self.log_file)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)
        
        # 配置日志格式
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger('SerialManagerDaemon')
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，准备退出...")
        self.stop()
    
    def start(self):
        """启动守护进程"""
        self.logger.info("启动串口管理器守护进程...")
        
        try:
            # 获取串口管理器实例
            self.manager = get_serial_manager()
            
            # 配置串口参数
            self.manager.port = self.port
            
            # 启动串口管理器
            if not self.manager.start():
                self.logger.error("无法启动串口管理器")
                return False
            
            self.running = True
            self.logger.info(f"串口管理器守护进程已启动，监听端口: {self.port}")
            
            # 保持运行状态
            while self.running:
                time.sleep(1.0)
                
                # 检查串口管理器状态
                if not self.manager.is_running:
                    self.logger.warning("串口管理器意外停止，尝试重启...")
                    if not self.manager.start():
                        self.logger.error("重启串口管理器失败")
                        break
                    else:
                        self.logger.info("串口管理器重启成功")
            
            return True
            
        except Exception as e:
            self.logger.error(f"启动守护进程失败: {e}")
            return False
        finally:
            self.stop()
    
    def stop(self):
        """停止守护进程"""
        if self.running:
            self.logger.info("停止串口管理器守护进程...")
            self.running = False
            
            if self.manager:
                self.manager.stop()
    
    def status(self):
        """获取守护进程状态"""
        if self.manager and self.manager.is_running:
            client_count = len(self.manager._clients)
            self.logger.info(f"串口管理器状态: 运行中, 已连接客户端: {client_count}")
            return True
        else:
            self.logger.info("串口管理器状态: 未运行")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='N100串口管理器守护进程')
    parser.add_argument('--port', type=str, default='/dev/ttyS4', 
                       help='串口设备路径 (默认: /dev/ttyS4)')
    parser.add_argument('--log-file', type=str, default='/var/log/n100_serial_manager.log',
                       help='日志文件路径 (默认: /var/log/n100_serial_manager.log)')
    parser.add_argument('--daemon', action='store_true',
                       help='以守护进程模式运行')
    parser.add_argument('--status', action='store_true',
                       help='查看守护进程状态')
    
    args = parser.parse_args()
    
    # 创建守护进程实例
    daemon = SerialManagerDaemon(port=args.port, log_file=args.log_file)
    
    # 如果只是查看状态
    if args.status:
        daemon.status()
        return
    
    if args.daemon:
        # 以守护进程模式运行
        try:
            import daemon as daemon_module
            with daemon_module.DaemonContext():
                success = daemon.start()
                sys.exit(0 if success else 1)
        except ImportError:
            print("错误: 需要安装python-daemon包才能以守护进程模式运行")
            print("请运行: pip install python-daemon")
            sys.exit(1)
    else:
        # 前台运行
        try:
            success = daemon.start()
            sys.exit(0 if success else 1)
        except KeyboardInterrupt:
            print("\n收到中断信号，正在退出...")
            daemon.stop()
            sys.exit(0)
        except Exception as e:
            print(f"守护进程异常退出: {e}")
            sys.exit(1)


if __name__ == "__main__":
    main()
