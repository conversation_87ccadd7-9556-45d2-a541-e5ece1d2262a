#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
N100电源控制系统测试运行器
运行所有测试并生成报告
"""

import os
import sys
import subprocess
import time
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

def run_unit_tests():
    """运行单元测试"""
    print("=" * 50)
    print("运行单元测试")
    print("=" * 50)
    
    unit_tests = [
        "unit/test_frame_format.py",
        "unit/test_communication.py"
    ]
    
    results = {}
    
    for test in unit_tests:
        test_path = Path(__file__).parent / test
        if test_path.exists():
            print(f"\n运行 {test}...")
            try:
                result = subprocess.run([sys.executable, str(test_path)], 
                                      capture_output=True, text=True, timeout=30)
                results[test] = {
                    'returncode': result.returncode,
                    'stdout': result.stdout,
                    'stderr': result.stderr
                }
                
                if result.returncode == 0:
                    print(f"✅ {test} - 通过")
                else:
                    print(f"❌ {test} - 失败")
                    if result.stderr:
                        print(f"错误: {result.stderr}")
                        
            except subprocess.TimeoutExpired:
                print(f"⏰ {test} - 超时")
                results[test] = {'returncode': -1, 'error': 'timeout'}
            except Exception as e:
                print(f"❌ {test} - 异常: {e}")
                results[test] = {'returncode': -1, 'error': str(e)}
        else:
            print(f"⚠️ {test} - 文件不存在")
    
    return results

def run_integration_tests():
    """运行集成测试"""
    print("\n" + "=" * 50)
    print("运行集成测试")
    print("=" * 50)
    
    integration_tests = [
        "integration/test_cli_with_simulator.py",
        "integration/test_shutdown_flow.py",
        "integration/test_shutdown_script.py",
        "integration/test_ack_fix.py"
    ]
    
    results = {}
    
    for test in integration_tests:
        test_path = Path(__file__).parent / test
        if test_path.exists():
            print(f"\n运行 {test}...")
            try:
                result = subprocess.run([sys.executable, str(test_path)], 
                                      capture_output=True, text=True, timeout=60)
                results[test] = {
                    'returncode': result.returncode,
                    'stdout': result.stdout,
                    'stderr': result.stderr
                }
                
                if result.returncode == 0:
                    print(f"✅ {test} - 通过")
                else:
                    print(f"❌ {test} - 失败")
                    if result.stderr:
                        print(f"错误: {result.stderr}")
                        
            except subprocess.TimeoutExpired:
                print(f"⏰ {test} - 超时")
                results[test] = {'returncode': -1, 'error': 'timeout'}
            except Exception as e:
                print(f"❌ {test} - 异常: {e}")
                results[test] = {'returncode': -1, 'error': str(e)}
        else:
            print(f"⚠️ {test} - 文件不存在")
    
    return results

def run_diagnostic_tools():
    """运行诊断工具"""
    print("\n" + "=" * 50)
    print("运行诊断工具")
    print("=" * 50)
    
    tools = [
        "tools/verify_fix.py"
    ]
    
    results = {}
    
    for tool in tools:
        tool_path = Path(__file__).parent / tool
        if tool_path.exists():
            print(f"\n运行 {tool}...")
            try:
                result = subprocess.run([sys.executable, str(tool_path)], 
                                      capture_output=True, text=True, timeout=30)
                results[tool] = {
                    'returncode': result.returncode,
                    'stdout': result.stdout,
                    'stderr': result.stderr
                }
                
                if result.returncode == 0:
                    print(f"✅ {tool} - 通过")
                else:
                    print(f"❌ {tool} - 失败")
                        
            except subprocess.TimeoutExpired:
                print(f"⏰ {tool} - 超时")
                results[tool] = {'returncode': -1, 'error': 'timeout'}
            except Exception as e:
                print(f"❌ {tool} - 异常: {e}")
                results[tool] = {'returncode': -1, 'error': str(e)}
        else:
            print(f"⚠️ {tool} - 文件不存在")
    
    return results

def generate_report(unit_results, integration_results, tool_results):
    """生成测试报告"""
    print("\n" + "=" * 50)
    print("测试报告")
    print("=" * 50)
    
    all_results = {
        "单元测试": unit_results,
        "集成测试": integration_results,
        "诊断工具": tool_results
    }
    
    total_tests = 0
    passed_tests = 0
    
    for category, results in all_results.items():
        print(f"\n{category}:")
        category_total = len(results)
        category_passed = sum(1 for r in results.values() 
                            if isinstance(r, dict) and r.get('returncode') == 0)
        
        print(f"  总计: {category_total}")
        print(f"  通过: {category_passed}")
        print(f"  失败: {category_total - category_passed}")
        
        total_tests += category_total
        passed_tests += category_passed
        
        # 显示失败的测试
        for test_name, result in results.items():
            if isinstance(result, dict) and result.get('returncode') != 0:
                print(f"  ❌ {test_name}")
    
    print(f"\n总体结果:")
    print(f"  总测试数: {total_tests}")
    print(f"  通过: {passed_tests}")
    print(f"  失败: {total_tests - passed_tests}")
    print(f"  成功率: {passed_tests/total_tests*100:.1f}%" if total_tests > 0 else "  成功率: 0%")
    
    return passed_tests == total_tests

def main():
    """主函数"""
    print("N100电源控制系统测试套件")
    print("=" * 50)
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行测试
    unit_results = run_unit_tests()
    integration_results = run_integration_tests()
    tool_results = run_diagnostic_tools()
    
    # 生成报告
    all_passed = generate_report(unit_results, integration_results, tool_results)
    
    if all_passed:
        print("\n🎉 所有测试通过！")
        return 0
    else:
        print("\n❌ 部分测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
